from __future__ import annotations

import asyncpg
import json
from datetime import datetime, timezone
from typing import Optional, Sequence

from optimed.core.domain import (
    JournalEntry, JournalInterpretation, DocumentMetadata, 
    DocumentFormat, JournalType, ProcessingStatus
)
from optimed.core.ports import JournalRepository


class PGJournalRepository(JournalRepository):
    """PostgreSQL-based journal repository."""

    def __init__(self, dsn: str, min_size: int = 5, max_size: int = 10):
        self.dsn = dsn
        self.min_size = min_size
        self.max_size = max_size
        self._pool = None

    async def _get_pool(self) -> asyncpg.Pool:
        """Get or create the connection pool."""
        if self._pool is None:
            self._pool = await asyncpg.create_pool(
                dsn=self.dsn,
                min_size=self.min_size,
                max_size=self.max_size,
            )
        return self._pool

    async def close(self) -> None:
        """Close the connection pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None

    async def store_journal(self, journal_entry: JournalEntry) -> str:
        """Store a journal entry."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO journals (
                    journal_id, patient_id, user_id, session_id, title, content,
                    journal_type, processing_status, uploaded_at, processed_at,
                    source_url, doi, pmid, journal_name, publication_date,
                    authors, abstract, sections, references, figures, tables,
                    metadata
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                    $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22
                )
                ON CONFLICT (journal_id) DO UPDATE SET
                    content = EXCLUDED.content,
                    processing_status = EXCLUDED.processing_status,
                    processed_at = EXCLUDED.processed_at,
                    updated_at = now()
                """,
                journal_entry.journal_id,
                journal_entry.patient_id,
                journal_entry.user_id,
                journal_entry.session_id,
                journal_entry.title,
                journal_entry.content,
                journal_entry.journal_type.value,
                journal_entry.processing_status.value,
                journal_entry.uploaded_at,
                journal_entry.processed_at,
                journal_entry.source_url,
                journal_entry.doi,
                journal_entry.pmid,
                journal_entry.journal_name,
                journal_entry.publication_date,
                json.dumps(journal_entry.authors),
                journal_entry.abstract,
                json.dumps(journal_entry.sections),
                json.dumps(journal_entry.references),
                json.dumps(journal_entry.figures),
                json.dumps(journal_entry.tables),
                json.dumps(journal_entry.metadata.model_dump())
            )
            return journal_entry.journal_id

    async def get_journal(self, journal_id: str) -> Optional[JournalEntry]:
        """Retrieve a journal entry by ID."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                """
                SELECT * FROM journals WHERE journal_id = $1
                """,
                journal_id
            )
            
            if not row:
                return None
            
            return self._row_to_journal_entry(row)

    async def store_interpretation(self, interpretation: JournalInterpretation) -> str:
        """Store a journal interpretation."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            await conn.execute(
                """
                INSERT INTO journal_interpretations (
                    interpretation_id, journal_id, summary, key_findings,
                    medical_insights, clinical_significance, patient_applicability,
                    treatment_implications, diagnostic_implications,
                    overall_confidence, confidence_level, quality_score,
                    evidence_level, interpreted_at, interpreted_by_model,
                    processing_time_seconds, diseases, medications,
                    procedures, symptoms, risk_factors
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                    $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
                )
                ON CONFLICT (interpretation_id) DO UPDATE SET
                    summary = EXCLUDED.summary,
                    key_findings = EXCLUDED.key_findings,
                    medical_insights = EXCLUDED.medical_insights,
                    clinical_significance = EXCLUDED.clinical_significance,
                    overall_confidence = EXCLUDED.overall_confidence,
                    updated_at = now()
                """,
                interpretation.interpretation_id,
                interpretation.journal_id,
                interpretation.summary,
                json.dumps(interpretation.key_findings),
                json.dumps([insight.model_dump() for insight in interpretation.medical_insights]),
                interpretation.clinical_significance,
                interpretation.patient_applicability,
                json.dumps(interpretation.treatment_implications),
                json.dumps(interpretation.diagnostic_implications),
                interpretation.overall_confidence,
                interpretation.confidence_level.value,
                interpretation.quality_score,
                interpretation.evidence_level,
                interpretation.interpreted_at,
                interpretation.interpreted_by_model,
                interpretation.processing_time_seconds,
                json.dumps(interpretation.diseases),
                json.dumps(interpretation.medications),
                json.dumps(interpretation.procedures),
                json.dumps(interpretation.symptoms),
                json.dumps(interpretation.risk_factors)
            )
            return interpretation.interpretation_id

    async def get_interpretation(self, journal_id: str) -> Optional[JournalInterpretation]:
        """Get interpretation for a journal."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                """
                SELECT * FROM journal_interpretations WHERE journal_id = $1
                """,
                journal_id
            )
            
            if not row:
                return None
            
            return self._row_to_interpretation(row)

    async def search_journals(
        self, 
        query: str, 
        journal_type: Optional[JournalType] = None,
        patient_id: Optional[str] = None,
        limit: int = 10
    ) -> Sequence[JournalEntry]:
        """Search journals by content or metadata."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            
            # Build dynamic query
            where_conditions = ["(title ILIKE $1 OR content ILIKE $1 OR abstract ILIKE $1)"]
            params = [f"%{query}%"]
            param_count = 1
            
            if journal_type:
                param_count += 1
                where_conditions.append(f"journal_type = ${param_count}")
                params.append(journal_type.value)
            
            if patient_id:
                param_count += 1
                where_conditions.append(f"patient_id = ${param_count}")
                params.append(patient_id)
            
            param_count += 1
            params.append(limit)
            
            query_sql = f"""
                SELECT * FROM journals 
                WHERE {' AND '.join(where_conditions)}
                ORDER BY uploaded_at DESC
                LIMIT ${param_count}
            """
            
            rows = await conn.fetch(query_sql, *params)
            
            return [self._row_to_journal_entry(row) for row in rows]

    async def get_patient_journals(self, patient_id: str) -> Sequence[JournalEntry]:
        """Get all journals associated with a patient."""
        pool = await self._get_pool()
        async with pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT * FROM journals 
                WHERE patient_id = $1
                ORDER BY uploaded_at DESC
                """,
                patient_id
            )
            
            return [self._row_to_journal_entry(row) for row in rows]

    def _row_to_journal_entry(self, row) -> JournalEntry:
        """Convert database row to JournalEntry."""
        metadata_dict = json.loads(row['metadata']) if row['metadata'] else {}
        
        return JournalEntry(
            journal_id=row['journal_id'],
            patient_id=row['patient_id'],
            user_id=row['user_id'],
            session_id=row['session_id'],
            title=row['title'],
            content=row['content'],
            journal_type=JournalType(row['journal_type']),
            metadata=DocumentMetadata(**metadata_dict),
            processing_status=ProcessingStatus(row['processing_status']),
            uploaded_at=row['uploaded_at'],
            processed_at=row['processed_at'],
            source_url=row['source_url'],
            doi=row['doi'],
            pmid=row['pmid'],
            journal_name=row['journal_name'],
            publication_date=row['publication_date'],
            authors=json.loads(row['authors']) if row['authors'] else [],
            abstract=row['abstract'],
            sections=json.loads(row['sections']) if row['sections'] else {},
            references=json.loads(row['references']) if row['references'] else [],
            figures=json.loads(row['figures']) if row['figures'] else [],
            tables=json.loads(row['tables']) if row['tables'] else [],
        )

    def _row_to_interpretation(self, row) -> JournalInterpretation:
        """Convert database row to JournalInterpretation."""
        from optimed.core.domain import MedicalInsight, InterpretationConfidence
        
        # Parse medical insights
        insights_data = json.loads(row['medical_insights']) if row['medical_insights'] else []
        medical_insights = [MedicalInsight(**insight_dict) for insight_dict in insights_data]
        
        return JournalInterpretation(
            interpretation_id=row['interpretation_id'],
            journal_id=row['journal_id'],
            summary=row['summary'],
            key_findings=json.loads(row['key_findings']) if row['key_findings'] else [],
            medical_insights=medical_insights,
            clinical_significance=row['clinical_significance'],
            patient_applicability=row['patient_applicability'],
            treatment_implications=json.loads(row['treatment_implications']) if row['treatment_implications'] else [],
            diagnostic_implications=json.loads(row['diagnostic_implications']) if row['diagnostic_implications'] else [],
            overall_confidence=row['overall_confidence'],
            confidence_level=InterpretationConfidence(row['confidence_level']),
            quality_score=row['quality_score'],
            evidence_level=row['evidence_level'],
            interpreted_at=row['interpreted_at'],
            interpreted_by_model=row['interpreted_by_model'],
            processing_time_seconds=row['processing_time_seconds'],
            diseases=json.loads(row['diseases']) if row['diseases'] else [],
            medications=json.loads(row['medications']) if row['medications'] else [],
            procedures=json.loads(row['procedures']) if row['procedures'] else [],
            symptoms=json.loads(row['symptoms']) if row['symptoms'] else [],
            risk_factors=json.loads(row['risk_factors']) if row['risk_factors'] else [],
        )
