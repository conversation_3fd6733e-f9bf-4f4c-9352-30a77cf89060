from __future__ import annotations

import chardet
from pathlib import Path

from optimed.core.domain import DocumentFormat
from .base import BaseDocumentParser


class TextParser(BaseDocumentParser):
    """Parser for plain text files."""

    def __init__(self):
        super().__init__()
        self.supported_formats = [
            DocumentFormat.TXT,
            DocumentFormat.MARKDOWN,
        ]

    async def _extract_content_from_file(self, file_path: str) -> str:
        """Extract text content from a text file."""
        path = Path(file_path)
        
        # Try to detect encoding
        with open(path, 'rb') as f:
            raw_data = f.read()
            
        encoding = self._detect_encoding(raw_data)
        
        # Read with detected encoding
        try:
            with open(path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            # Fallback to utf-8 with error handling
            with open(path, 'r', encoding='utf-8', errors='replace') as f:
                return f.read()

    async def _extract_content_from_bytes(self, data: bytes) -> str:
        """Extract text content from bytes."""
        encoding = self._detect_encoding(data)
        
        try:
            return data.decode(encoding)
        except UnicodeDecodeError:
            # Fallback to utf-8 with error handling
            return data.decode('utf-8', errors='replace')

    def _detect_encoding(self, data: bytes) -> str:
        """Detect text encoding."""
        if not data:
            return 'utf-8'
            
        # Use chardet for encoding detection
        result = chardet.detect(data)
        encoding = result.get('encoding', 'utf-8')
        
        # Fallback to common encodings if detection fails
        if not encoding or result.get('confidence', 0) < 0.7:
            # Try common encodings
            for enc in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    data.decode(enc)
                    return enc
                except UnicodeDecodeError:
                    continue
            return 'utf-8'  # Final fallback
            
        return encoding

    def _extract_title(self, content: str) -> str | None:
        """Extract title from text content."""
        lines = content.strip().split('\n')
        
        # For markdown, look for # headers
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
        
        # Otherwise use base implementation
        return super()._extract_title(content)
