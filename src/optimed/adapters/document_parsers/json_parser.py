from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, List

from optimed.core.domain import DocumentFormat, DocumentMetadata
from .base import BaseDocumentParser


class JSONParser(BaseDocumentParser):
    """Parser for JSON files containing medical data."""

    def __init__(self):
        super().__init__()
        self.supported_formats = [DocumentFormat.JSON]

    async def _extract_content_from_file(self, file_path: str) -> str:
        """Extract text content from a JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        return self._extract_text_from_json(data)

    async def _extract_content_from_bytes(self, data: bytes) -> str:
        """Extract text content from JSON bytes."""
        json_data = json.loads(data.decode('utf-8'))
        return self._extract_text_from_json(json_data)

    def _extract_text_from_json(self, data: Any) -> str:
        """Extract readable text from JSON data structure."""
        if isinstance(data, dict):
            return self._extract_from_dict(data)
        elif isinstance(data, list):
            return self._extract_from_list(data)
        else:
            return str(data)

    def _extract_from_dict(self, data: Dict[str, Any]) -> str:
        """Extract text from a dictionary."""
        text_parts = []
        
        # Common medical JSON fields to prioritize
        priority_fields = [
            'title', 'abstract', 'summary', 'content', 'text', 'description',
            'diagnosis', 'findings', 'impression', 'conclusion', 'notes',
            'chief_complaint', 'history', 'examination', 'assessment', 'plan'
        ]
        
        # Extract priority fields first
        for field in priority_fields:
            if field in data:
                value = data[field]
                if isinstance(value, str) and value.strip():
                    text_parts.append(f"{field.title()}: {value}")
                elif isinstance(value, (dict, list)):
                    nested_text = self._extract_text_from_json(value)
                    if nested_text.strip():
                        text_parts.append(f"{field.title()}: {nested_text}")
        
        # Extract other fields
        for key, value in data.items():
            if key.lower() not in [f.lower() for f in priority_fields]:
                if isinstance(value, str) and value.strip():
                    text_parts.append(f"{key}: {value}")
                elif isinstance(value, (dict, list)):
                    nested_text = self._extract_text_from_json(value)
                    if nested_text.strip():
                        text_parts.append(f"{key}: {nested_text}")
                elif value is not None:
                    text_parts.append(f"{key}: {str(value)}")
        
        return '\n\n'.join(text_parts)

    def _extract_from_list(self, data: List[Any]) -> str:
        """Extract text from a list."""
        text_parts = []
        
        for i, item in enumerate(data):
            if isinstance(item, str) and item.strip():
                text_parts.append(item)
            elif isinstance(item, (dict, list)):
                nested_text = self._extract_text_from_json(item)
                if nested_text.strip():
                    text_parts.append(nested_text)
            elif item is not None:
                text_parts.append(str(item))
        
        return '\n'.join(text_parts)

    def _extract_title(self, content: str) -> str | None:
        """Extract title from JSON content."""
        # Look for title in the first few lines
        lines = content.split('\n')
        for line in lines[:5]:
            line = line.strip()
            if line.lower().startswith('title:'):
                return line[6:].strip()
        
        return super()._extract_title(content)

    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from JSON content."""
        keywords = super()._extract_keywords(content)
        
        # Look for JSON-specific medical fields
        json_medical_fields = [
            'diagnosis', 'findings', 'impression', 'assessment', 'plan',
            'chief_complaint', 'history', 'examination', 'medication',
            'allergy', 'vital_signs', 'laboratory', 'imaging'
        ]
        
        content_lower = content.lower()
        for field in json_medical_fields:
            if field in content_lower and field not in keywords:
                keywords.append(field)
        
        return keywords[:10]

    def _analyze_content(self, content: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Analyze JSON content and update metadata."""
        # Get base analysis
        updated_metadata = super()._analyze_content(content, metadata)
        
        # JSON files don't have pages in traditional sense
        return updated_metadata.model_copy(update={
            "page_count": 1,
        })
