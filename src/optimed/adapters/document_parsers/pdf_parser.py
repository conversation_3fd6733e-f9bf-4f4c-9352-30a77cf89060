from __future__ import annotations

import io
from pathlib import Path
from typing import Optional

try:
    import PyPDF2
    HAS_PYPDF2 = True
except ImportError:
    HAS_PYPDF2 = False

try:
    import pdfplumber
    HAS_PDFPLUMBER = True
except ImportError:
    HAS_PDFPLUMBER = False

from optimed.core.domain import DocumentFormat, DocumentMetadata
from .base import BaseDocumentParser


class PDFParser(BaseDocumentParser):
    """Parser for PDF files using PyPDF2 or pdfplumber."""

    def __init__(self, prefer_pdfplumber: bool = True):
        super().__init__()
        self.supported_formats = [DocumentFormat.PDF]
        self.prefer_pdfplumber = prefer_pdfplumber
        
        if not HAS_PYPDF2 and not HAS_PDFPLUMBER:
            raise ImportError(
                "PDF parsing requires either PyPDF2 or pdfplumber. "
                "Install with: pip install PyPDF2 pdfplumber"
            )

    async def _extract_content_from_file(self, file_path: str) -> str:
        """Extract text content from a PDF file."""
        path = Path(file_path)
        
        if self.prefer_pdfplumber and HAS_PDFPLUMBER:
            return await self._extract_with_pdfplumber(str(path))
        elif HAS_PYPDF2:
            return await self._extract_with_pypdf2(str(path))
        else:
            raise RuntimeError("No PDF parsing library available")

    async def _extract_content_from_bytes(self, data: bytes) -> str:
        """Extract text content from PDF bytes."""
        if self.prefer_pdfplumber and HAS_PDFPLUMBER:
            return await self._extract_bytes_with_pdfplumber(data)
        elif HAS_PYPDF2:
            return await self._extract_bytes_with_pypdf2(data)
        else:
            raise RuntimeError("No PDF parsing library available")

    async def _extract_with_pdfplumber(self, file_path: str) -> str:
        """Extract text using pdfplumber (better for complex layouts)."""
        import pdfplumber
        
        text_parts = []
        
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(page_text)
        
        return '\n\n'.join(text_parts)

    async def _extract_bytes_with_pdfplumber(self, data: bytes) -> str:
        """Extract text from PDF bytes using pdfplumber."""
        import pdfplumber
        
        text_parts = []
        
        with pdfplumber.open(io.BytesIO(data)) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(page_text)
        
        return '\n\n'.join(text_parts)

    async def _extract_with_pypdf2(self, file_path: str) -> str:
        """Extract text using PyPDF2 (faster but less accurate)."""
        import PyPDF2
        
        text_parts = []
        
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(page_text)
        
        return '\n\n'.join(text_parts)

    async def _extract_bytes_with_pypdf2(self, data: bytes) -> str:
        """Extract text from PDF bytes using PyPDF2."""
        import PyPDF2
        
        text_parts = []
        
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(data))
        
        for page in pdf_reader.pages:
            page_text = page.extract_text()
            if page_text:
                text_parts.append(page_text)
        
        return '\n\n'.join(text_parts)

    def _analyze_content(self, content: str, metadata: DocumentMetadata) -> DocumentMetadata:
        """Analyze PDF content and update metadata."""
        # Get base analysis
        updated_metadata = super()._analyze_content(content, metadata)
        
        # PDF-specific analysis
        page_count = self._estimate_page_count(content)
        
        return updated_metadata.model_copy(update={
            "page_count": page_count,
        })

    def _estimate_page_count(self, content: str) -> int:
        """Estimate page count from content."""
        # Simple heuristic: assume ~500 words per page
        word_count = len(content.split())
        return max(1, word_count // 500)

    def _extract_title(self, content: str) -> str | None:
        """Extract title from PDF content."""
        lines = content.strip().split('\n')
        
        # Look for title patterns in first few lines
        for i, line in enumerate(lines[:10]):  # Check first 10 lines
            line = line.strip()
            if line and len(line) > 10 and len(line) < 200:
                # Check if this looks like a title (not just metadata)
                if not any(keyword in line.lower() for keyword in ['page', 'doi:', 'issn:', 'volume', 'number']):
                    return line
        
        return super()._extract_title(content)
