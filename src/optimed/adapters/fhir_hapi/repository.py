from __future__ import annotations
import os
import uuid
from datetime import datetime, timezone
from typing import Sequence
from pydantic import ValidationError

import httpx
from fhir.resources.patient import Patient # type: ignore[import-untyped]
from fhir.resources.observation import Observation # type: ignore[import-untyped]
from fhir.resources.documentreference import DocumentReference # type: ignore[import-untyped]
from fhir.resources.diagnosticreport import DiagnosticReport # type: ignore[import-untyped]

from optimed.core.domain import PatientContext, JournalEntry, JournalType, ProcessingStatus, DocumentMetadata, DocumentFormat
from optimed.core.ports import FHIRRepository

"""
optimed.adapters.fhir_hapi.repository
-------------------------------------

Read-only FHIR adapter that talks to the public HAPI R4 demo server
(https://hapi.fhir.org/baseR4) and converts resources into the project’s
domain models.

✓ Implements FHIRRepository (core.ports)
✓ Async/await-friendly via httpx.AsyncClient
✓ No authentication – perfect for CI and early prototypes
"""

# --------------------------------------------------------------------------- #
# Configuration
# --------------------------------------------------------------------------- #

DEFAULT_BASE_URL = "https://hapi.fhir.org/baseR4"
FHIR_BASE_URL = os.getenv("FHIR_BASE_URL", DEFAULT_BASE_URL).rstrip("/")

# LOINC codes for quick MVP mapping  (add more when needed)
VITAL_CODES = {
    "8867-4": "Heart rate",
    "59408-5": "Resp rate",
    "8480-6": "Systolic BP",
    "8462-4": "Diastolic BP",
    "8310-5": "Body temperature",
}

CRITICAL_LAB_CODES = {
    "2823-3": "Potassium",         # used by PatientContext.has_critical_lab()
    "2339-0": "Glucose",
}

# --------------------------------------------------------------------------- #
# Helper functions
# --------------------------------------------------------------------------- #

def _age_from_birthdate(date) -> int:
    """Calculate age in years from FHIR birthDate."""
    if not date:
        return 0
    today = datetime.now(timezone.utc).date()
    return max(0, (today - date).days // 365)

def _fhir_name_to_str(p: Patient) -> str:
    """Return best effort full name"""

    if not p.name:
        return "Unknown"
    n = p.name[0]
    if n.text:
        return n.text
    parts = []
    if n.given:
        parts.extend(n.given)
    if n.family:
        parts.append(n.family)
    return " ".join(parts).strip() or "Unknown"

def _obs_bundle_to_dict(entries: list[Observation]) -> dict[str, str]:
    """Convert a list of Observations to a dict of code → value"""
    out: dict[str, str] = {}
    for obs in entries:
        if not obs.code or not obs.code.coding:
            continue
        code = obs.code.coding[0].code
        if not obs.valueQuantity:
            continue
        value = obs.valueQuantity.value
        unit = obs.valueQuantity.unit 
        out[code] = f"{value} {unit}"

    return out 

def _to_patient_ctx(
        patient: Patient,
        vitals: list[Observation] | None = None,
        labs: list[Observation] | None = None,
) -> PatientContext:
    """Convert FHIR Patient + vitals/labs to PatientContext domain model."""
    if not patient.id:
        raise ValueError("Patient must have an ID")

    vitals_dict = _obs_bundle_to_dict(vitals or [])
    labs_dict = _obs_bundle_to_dict(labs or [])

    return PatientContext(
        patient_id=patient.id,
        name=_fhir_name_to_str(patient),
        age=_age_from_birthdate(patient.birthDate),
        sex=patient.gender or "unknown",
        care_unit="UNKOWN",  # HAPI demo has no care unit info
        vitals=vitals_dict,
        labs=labs_dict,
    )


# --------------------------------------------------------------------------- #
# Adapter class
# --------------------------------------------------------------------------- #

class HAPIFHIRRepository(FHIRRepository):
    """Read-only FHIR repository using the HAPI FHIR demo server."""

    def __init__(
        self,
        *,
        base_url: str | None = None,
        timeout: float = 10.0,
        client: httpx.AsyncClient | None = None,
    ) -> None:
        self._base = (base_url or FHIR_BASE_URL).rstrip("/")
        self._client = client or httpx.AsyncClient(base_url=self._base, timeout=timeout, headers={"User-Agent": "OptiMedPrototype/0.1"})


    async def get_patient(self, patient_id: str) -> PatientContext:
        patient = await self._get_patient_resource(patient_id)
        vitals = await self._get_observations(patient_id, category="vital-signs")
        labs = await self._get_observations(patient_id, category="laboratory")
        return _to_patient_ctx(patient, vitals, labs)

    async def get_patient_clinical_notes(self, patient_id: str) -> list[JournalEntry]:
        """Fetch clinical notes from FHIR and convert to journal entries."""
        try:
            # Fetch DocumentReference resources (clinical notes, discharge summaries, etc.)
            document_refs = await self._get_document_references(patient_id)

            # Fetch DiagnosticReport resources (lab reports with narrative)
            diagnostic_reports = await self._get_diagnostic_reports(patient_id)

            journal_entries = []

            # Convert DocumentReference to JournalEntry
            for doc_ref in document_refs:
                journal_entry = await self._document_reference_to_journal(doc_ref, patient_id)
                if journal_entry:
                    journal_entries.append(journal_entry)

            # Convert DiagnosticReport to JournalEntry
            for report in diagnostic_reports:
                journal_entry = await self._diagnostic_report_to_journal(report, patient_id)
                if journal_entry:
                    journal_entries.append(journal_entry)

            return journal_entries

        except Exception as e:
            print(f"Error fetching clinical notes for patient {patient_id}: {e}")
            return []
    
    async def search_patients(self, query: str) -> Sequence[PatientContext]:
        """Search patients by MRN or name."""
        
        r = await self._client.get("/Patient", params={"name": query, "_count": 10})
        r.raise_for_status()
        bundle = r.json()

        results: list[PatientContext] = []
        for entry in bundle.get("entry", []):
            # drop the top‐level "text" block so Patient.model_validate won't expect text.div
            resource = entry["resource"].copy()
            resource.pop("text", None)
            try:
                p = Patient.model_validate(resource)
            except ValidationError:
                # skip any resource that still fails validation
                continue
            results.append(_to_patient_ctx(p, [], []))

        return results
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self._client.aclose()

    async def _get_patient_resource(self, pid: str) -> Patient:
        """Fetch a single Patient resource by ID."""
        r = await self._client.get(f"/Patient/{pid}")
        r.raise_for_status()
        return Patient.model_validate(r.json())
    
    async def _get_observations(
            self,
            pid: str,
            *,
            category: str,
            count: int = 10
    ) -> list[Observation]:
        """Fetch Observations of a given category for a patient."""
        r = await self._client.get(
            "/Observation",
            params={
                "patient": pid,
                "category": category,
                "_sort": "-date",
                "_count": count,
            },
        )
        r.raise_for_status()
        bundle = r.json()
        good: list[Observation] = []
        for entry in bundle.get("entry", []):
            try:
                good.append(Observation.model_validate(entry["resource"]))
            except ValidationError:
                # HAPI demo sometimes omits timezone in effectiveDateTime → skip
                continue
        return good

    async def __aenter__(self):
        return self
    
    async def __aexit__(self, *exc):
        await self.close()

    async def _get_document_references(self, patient_id: str, count: int = 10) -> list[DocumentReference]:
        """Fetch DocumentReference resources for a patient."""
        try:
            r = await self._client.get(
                "/DocumentReference",
                params={
                    "patient": patient_id,
                    "_sort": "-date",
                    "_count": count,
                },
            )
            r.raise_for_status()
            bundle = r.json()

            document_refs: list[DocumentReference] = []
            for entry in bundle.get("entry", []):
                try:
                    doc_ref = DocumentReference.model_validate(entry["resource"])
                    document_refs.append(doc_ref)
                except ValidationError:
                    # Skip invalid resources
                    continue

            return document_refs
        except Exception as e:
            print(f"Error fetching DocumentReference for patient {patient_id}: {e}")
            return []

    async def _get_diagnostic_reports(self, patient_id: str, count: int = 10) -> list[DiagnosticReport]:
        """Fetch DiagnosticReport resources for a patient."""
        try:
            r = await self._client.get(
                "/DiagnosticReport",
                params={
                    "patient": patient_id,
                    "_sort": "-date",
                    "_count": count,
                },
            )
            r.raise_for_status()
            bundle = r.json()

            reports: list[DiagnosticReport] = []
            for entry in bundle.get("entry", []):
                try:
                    report = DiagnosticReport.model_validate(entry["resource"])
                    reports.append(report)
                except ValidationError:
                    # Skip invalid resources
                    continue

            return reports
        except Exception as e:
            print(f"Error fetching DiagnosticReport for patient {patient_id}: {e}")
            return []

    async def _document_reference_to_journal(self, doc_ref: DocumentReference, patient_id: str) -> JournalEntry | None:
        """Convert FHIR DocumentReference to JournalEntry."""
        try:
            # Extract content from DocumentReference
            content = ""
            if doc_ref.content and len(doc_ref.content) > 0:
                attachment = doc_ref.content[0].attachment
                if attachment and attachment.data:
                    # For base64 encoded content
                    import base64
                    try:
                        content = base64.b64decode(attachment.data).decode('utf-8')
                    except Exception:
                        content = attachment.data  # Fallback to raw data
                elif attachment and attachment.url:
                    # For URL-referenced content, we'd need to fetch it
                    content = f"Content available at: {attachment.url}"

            # Use description or title as content if no attachment content
            if not content and doc_ref.description:
                content = doc_ref.description

            if not content:
                return None  # Skip if no content available

            # Determine journal type based on document type
            journal_type = self._determine_journal_type_from_doc_ref(doc_ref)

            # Create metadata
            content_bytes = content.encode('utf-8') if isinstance(content, str) else content
            metadata = DocumentMetadata(
                file_name=f"clinical_note_{doc_ref.id}.txt",
                file_size=len(content_bytes),
                format=DocumentFormat.TXT,
                mime_type="text/plain",
                created_at=doc_ref.date,
                title=doc_ref.description or "Clinical Note"
            )

            # Create journal entry
            journal_entry = JournalEntry(
                journal_id=f"fhir_doc_{doc_ref.id}",
                patient_id=patient_id,
                title=doc_ref.description or f"Clinical Note - {doc_ref.type.text if doc_ref.type else 'Unknown'}",
                content=content,
                journal_type=journal_type,
                metadata=metadata,
                processing_status=ProcessingStatus.PENDING,
                uploaded_at=doc_ref.date or datetime.now(timezone.utc)
            )

            return journal_entry

        except Exception as e:
            print(f"Error converting DocumentReference {doc_ref.id} to journal: {e}")
            return None

    async def _diagnostic_report_to_journal(self, report: DiagnosticReport, patient_id: str) -> JournalEntry | None:
        """Convert FHIR DiagnosticReport to JournalEntry."""
        try:
            # Extract content from DiagnosticReport
            content_parts = []

            # Add conclusion/summary
            if report.conclusion:
                content_parts.append(f"Conclusion: {report.conclusion}")

            # Add presentation text if available
            if hasattr(report, 'presentedForm') and report.presentedForm:
                for form in report.presentedForm:
                    if form.data:
                        import base64
                        try:
                            decoded_content = base64.b64decode(form.data).decode('utf-8')
                            content_parts.append(decoded_content)
                        except Exception:
                            content_parts.append(form.data)

            # Add result observations if available
            if hasattr(report, 'result') and report.result:
                content_parts.append("Results:")
                for result_ref in report.result[:5]:  # Limit to first 5 results
                    if hasattr(result_ref, 'display') and result_ref.display:
                        content_parts.append(f"- {result_ref.display}")

            # Ensure all content parts are strings
            content_parts = [str(part) for part in content_parts if part]
            content = "\n\n".join(content_parts)

            if not content:
                return None  # Skip if no content available

            # Create metadata
            content_bytes = content.encode('utf-8') if isinstance(content, str) else content
            metadata = DocumentMetadata(
                file_name=f"diagnostic_report_{report.id}.txt",
                file_size=len(content_bytes),
                format=DocumentFormat.TXT,
                mime_type="text/plain",
                created_at=report.effectiveDateTime,
                title=f"Diagnostic Report - {report.code.text if report.code else 'Unknown'}"
            )

            # Create journal entry
            journal_entry = JournalEntry(
                journal_id=f"fhir_report_{report.id}",
                patient_id=patient_id,
                title=f"Diagnostic Report - {report.code.text if report.code else 'Lab Report'}",
                content=content,
                journal_type=JournalType.LAB_REPORT,
                metadata=metadata,
                processing_status=ProcessingStatus.PENDING,
                uploaded_at=report.effectiveDateTime or datetime.now(timezone.utc)
            )

            return journal_entry

        except Exception as e:
            print(f"Error converting DiagnosticReport {report.id} to journal: {e}")
            return None

    def _determine_journal_type_from_doc_ref(self, doc_ref: DocumentReference) -> JournalType:
        """Determine journal type based on DocumentReference type."""
        if not doc_ref.type or not doc_ref.type.coding:
            return JournalType.PROGRESS_NOTE

        # Check LOINC codes for common document types
        for coding in doc_ref.type.coding:
            code = coding.code.lower() if coding.code else ""
            display = coding.display.lower() if coding.display else ""

            # Discharge summaries
            if "discharge" in display or "summary" in display:
                return JournalType.DISCHARGE_SUMMARY

            # Progress notes
            if "progress" in display or "note" in display:
                return JournalType.PROGRESS_NOTE

            # Consultation notes
            if "consult" in display or "consultation" in display:
                return JournalType.PROGRESS_NOTE

            # Procedure notes
            if "procedure" in display or "operative" in display:
                return JournalType.PROGRESS_NOTE

        # Default to progress note
        return JournalType.PROGRESS_NOTE