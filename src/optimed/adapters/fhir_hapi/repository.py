from __future__ import annotations
import os
import base64
from datetime import datetime, timezone
from typing import Sequence
from pydantic import ValidationError

import httpx
from fhir.resources.patient import Patient # type: ignore[import-untyped]
from fhir.resources.observation import Observation # type: ignore[import-untyped]
from fhir.resources.documentreference import DocumentReference # type: ignore[import-untyped]

from optimed.core.domain import PatientContext
from optimed.core.ports import FHIRRepository

"""
optimed.adapters.fhir_hapi.repository
-------------------------------------

Read-only FHIR adapter that talks to the public HAPI R4 demo server
(https://hapi.fhir.org/baseR4) and converts resources into the project’s
domain models.

✓ Implements FHIRRepository (core.ports)
✓ Async/await-friendly via httpx.AsyncClient
✓ No authentication – perfect for CI and early prototypes
"""

# --------------------------------------------------------------------------- #
# Configuration
# --------------------------------------------------------------------------- #

DEFAULT_BASE_URL = "https://hapi.fhir.org/baseR4"
FHIR_BASE_URL = os.getenv("FHIR_BASE_URL", DEFAULT_BASE_URL).rstrip("/")

# LOINC codes for quick MVP mapping  (add more when needed)
VITAL_CODES = {
    "8867-4": "Heart rate",
    "59408-5": "Resp rate",
    "8480-6": "Systolic BP",
    "8462-4": "Diastolic BP",
    "8310-5": "Body temperature",
}

CRITICAL_LAB_CODES = {
    "2823-3": "Potassium",         # used by PatientContext.has_critical_lab()
    "2339-0": "Glucose",
}

# --------------------------------------------------------------------------- #
# Helper functions
# --------------------------------------------------------------------------- #

def _age_from_birthdate(date) -> int:
    """Calculate age in years from FHIR birthDate."""
    if not date:
        return 0
    today = datetime.now(timezone.utc).date()
    return max(0, (today - date).days // 365)

def _fhir_name_to_str(p: Patient) -> str:
    """Return best effort full name"""

    if not p.name:
        return "Unknown"
    n = p.name[0]
    if n.text:
        return n.text
    parts = []
    if n.given:
        parts.extend(n.given)
    if n.family:
        parts.append(n.family)
    return " ".join(parts).strip() or "Unknown"

def _obs_bundle_to_dict(entries: list[Observation]) -> dict[str, str]:
    """Convert a list of Observations to a dict of code → value"""
    out: dict[str, str] = {}
    for obs in entries:
        if not obs.code or not obs.code.coding:
            continue
        code = obs.code.coding[0].code
        if not obs.valueQuantity:
            continue
        value = obs.valueQuantity.value
        unit = obs.valueQuantity.unit 
        out[code] = f"{value} {unit}"

    return out 

def _to_patient_ctx(
        patient: Patient,
        vitals: list[Observation] | None = None,
        labs: list[Observation] | None = None,
) -> PatientContext:
    """Convert FHIR Patient + vitals/labs to PatientContext domain model."""
    if not patient.id:
        raise ValueError("Patient must have an ID")

    vitals_dict = _obs_bundle_to_dict(vitals or [])
    labs_dict = _obs_bundle_to_dict(labs or [])

    return PatientContext(
        patient_id=patient.id,
        name=_fhir_name_to_str(patient),
        age=_age_from_birthdate(patient.birthDate),
        sex=patient.gender or "unknown",
        care_unit="UNKOWN",  # HAPI demo has no care unit info
        vitals=vitals_dict,
        labs=labs_dict,
    )


# --------------------------------------------------------------------------- #
# Adapter class
# --------------------------------------------------------------------------- #

class HAPIFHIRRepository(FHIRRepository):
    """Read-only FHIR repository using the HAPI FHIR demo server."""

    def __init__(
        self,
        *,
        base_url: str | None = None,
        timeout: float = 10.0,
        client: httpx.AsyncClient | None = None,
    ) -> None:
        self._base = (base_url or FHIR_BASE_URL).rstrip("/")
        self._client = client or httpx.AsyncClient(base_url=self._base, timeout=timeout, headers={"User-Agent": "OptiMedPrototype/0.1"})


    async def get_patient(self, patient_id: str) -> PatientContext:
        patient = await self._get_patient_resource(patient_id)
        vitals = await self._get_observations(patient_id, category="vital-signs")
        labs = await self._get_observations(patient_id, category="laboratory")
        return _to_patient_ctx(patient, vitals, labs)

    async def get_patient_clinical_notes(self, patient_id: str) -> list:
        """
        Fetch clinical notes from FHIR DocumentReference resources.

        Args:
            patient_id: The patient ID to fetch clinical notes for.

        Returns:
            A list of JournalEntry objects representing the clinical notes.
        """
        from optimed.core.domain import JournalEntry, JournalType, ProcessingStatus, DocumentMetadata, DocumentFormat

        try:
            # Query FHIR server for DocumentReference resources linked to the patient
            document_references = await self._get_document_references(patient_id)

            # Parse the FHIR response and convert to JournalEntry objects
            journal_entries = []
            for doc_ref in document_references:
                try:
                    journal_entry = JournalEntry(
                        journal_id=f"fhir_doc_{doc_ref.id}",
                        patient_id=patient_id,
                        title=self._extract_document_title(doc_ref),
                        content=self._extract_content_from_document_reference(doc_ref),
                        journal_type=self._map_document_type(doc_ref),
                        metadata=self._create_metadata_from_document_reference(doc_ref),
                        processing_status=ProcessingStatus.COMPLETED,
                        uploaded_at=self._parse_document_date(doc_ref),
                        source="EHR"
                    )
                    journal_entries.append(journal_entry)

                except Exception as e:
                    print(f"Error converting DocumentReference {doc_ref.id} to JournalEntry: {e}")
                    continue

            # If no real documents found, provide fallback mock data for demo
            if not journal_entries:
                print(f"No DocumentReference resources found for patient {patient_id}, using fallback data")
                journal_entries = self._create_fallback_clinical_notes(patient_id)

            return journal_entries

        except Exception as e:
            print(f"Error fetching clinical notes for patient {patient_id}: {e}")
            # Return fallback data on error
            return self._create_fallback_clinical_notes(patient_id)
    
    async def search_patients(self, query: str) -> Sequence[PatientContext]:
        """Search patients by MRN or name."""
        
        r = await self._client.get("/Patient", params={"name": query, "_count": 10})
        r.raise_for_status()
        bundle = r.json()

        results: list[PatientContext] = []
        for entry in bundle.get("entry", []):
            # drop the top‐level "text" block so Patient.model_validate won't expect text.div
            resource = entry["resource"].copy()
            resource.pop("text", None)
            try:
                p = Patient.model_validate(resource)
            except ValidationError:
                # skip any resource that still fails validation
                continue
            results.append(_to_patient_ctx(p, [], []))

        return results
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self._client.aclose()

    async def _get_patient_resource(self, pid: str) -> Patient:
        """Fetch a single Patient resource by ID."""
        r = await self._client.get(f"/Patient/{pid}")
        r.raise_for_status()
        return Patient.model_validate(r.json())
    
    async def _get_observations(
            self,
            pid: str,
            *,
            category: str,
            count: int = 10
    ) -> list[Observation]:
        """Fetch Observations of a given category for a patient."""
        r = await self._client.get(
            "/Observation",
            params={
                "patient": pid,
                "category": category,
                "_sort": "-date",
                "_count": count,
            },
        )
        r.raise_for_status()
        bundle = r.json()
        good: list[Observation] = []
        for entry in bundle.get("entry", []):
            try:
                good.append(Observation.model_validate(entry["resource"]))
            except ValidationError:
                # HAPI demo sometimes omits timezone in effectiveDateTime → skip
                continue
        return good

    async def _get_document_references(self, patient_id: str, count: int = 20) -> list[DocumentReference]:
        """
        Fetch DocumentReference resources for a patient.

        Args:
            patient_id: The patient ID to fetch documents for.
            count: Maximum number of documents to retrieve.

        Returns:
            List of DocumentReference objects.
        """
        try:
            r = await self._client.get(
                "/DocumentReference",
                params={
                    "subject": f"Patient/{patient_id}",
                    "_sort": "-date",
                    "_count": count,
                },
            )
            r.raise_for_status()
            bundle = r.json()

            documents: list[DocumentReference] = []
            for entry in bundle.get("entry", []):
                try:
                    documents.append(DocumentReference.model_validate(entry["resource"]))
                except ValidationError as e:
                    print(f"Validation error for DocumentReference: {e}")
                    continue

            return documents

        except Exception as e:
            print(f"Error fetching DocumentReference resources: {e}")
            return []

    def _extract_document_title(self, doc_ref: DocumentReference) -> str:
        """Extract title from DocumentReference."""
        if hasattr(doc_ref, 'description') and doc_ref.description:
            return doc_ref.description
        elif hasattr(doc_ref, 'type') and doc_ref.type and doc_ref.type.text:
            return doc_ref.type.text
        elif hasattr(doc_ref, 'content') and doc_ref.content and len(doc_ref.content) > 0:
            attachment = doc_ref.content[0].attachment
            if hasattr(attachment, 'title') and attachment.title:
                return attachment.title
        return f"Clinical Document {doc_ref.id}"

    def _extract_content_from_document_reference(self, doc_ref: DocumentReference) -> str:
        """
        Extract the content from a FHIR DocumentReference resource.

        Args:
            doc_ref: The FHIR DocumentReference resource.

        Returns:
            The decoded content as a string.
        """
        try:
            if not hasattr(doc_ref, 'content') or not doc_ref.content:
                return "No content available"

            attachment = doc_ref.content[0].attachment

            if hasattr(attachment, 'data') and attachment.data:
                # Decode base64 content
                decoded_content = base64.b64decode(attachment.data).decode("utf-8")
                return decoded_content
            elif hasattr(attachment, 'url') and attachment.url:
                # Handle external URLs if provided
                return f"Content available at: {attachment.url}"
            else:
                return "No content data available"

        except Exception as e:
            print(f"Error extracting content from DocumentReference: {e}")
            return "Error extracting content"

    def _map_document_type(self, doc_ref: DocumentReference):
        """
        Map the FHIR DocumentReference type to a JournalType.

        Args:
            doc_ref: The FHIR DocumentReference resource.

        Returns:
            The corresponding JournalType.
        """
        from optimed.core.domain import JournalType

        try:
            if hasattr(doc_ref, 'type') and doc_ref.type and doc_ref.type.coding:
                type_coding = doc_ref.type.coding[0]
                code = type_coding.code if hasattr(type_coding, 'code') else ""

                # Map common LOINC codes to journal types
                if code == "18842-5":  # Discharge summary
                    return JournalType.DISCHARGE_SUMMARY
                elif code == "11506-3":  # Progress note
                    return JournalType.PROGRESS_NOTE
                elif code == "11488-4":  # Consultation note
                    return JournalType.CONSULTATION_NOTE
                elif code == "28570-0":  # Procedure note
                    return JournalType.PROCEDURE_NOTE
                elif code in ["18748-4", "18761-7"]:  # Lab reports
                    return JournalType.LAB_REPORT
                elif code in ["18748-4", "18782-3"]:  # Radiology reports
                    return JournalType.RADIOLOGY_REPORT

            # Default based on document description or type text
            title = self._extract_document_title(doc_ref).lower()
            if "discharge" in title:
                return JournalType.DISCHARGE_SUMMARY
            elif "progress" in title or "daily" in title:
                return JournalType.PROGRESS_NOTE
            elif "lab" in title or "laboratory" in title:
                return JournalType.LAB_REPORT
            elif "consultation" in title or "consult" in title:
                return JournalType.CONSULTATION_NOTE
            elif "procedure" in title or "operative" in title:
                return JournalType.PROCEDURE_NOTE
            elif "radiology" in title or "imaging" in title:
                return JournalType.RADIOLOGY_REPORT
            else:
                return JournalType.PROGRESS_NOTE  # Default fallback

        except Exception as e:
            print(f"Error mapping document type: {e}")
            from optimed.core.domain import JournalType
            return JournalType.PROGRESS_NOTE

    def _create_metadata_from_document_reference(self, doc_ref: DocumentReference):
        """
        Create metadata for a clinical note from DocumentReference.

        Args:
            doc_ref: The FHIR DocumentReference resource.

        Returns:
            A DocumentMetadata object.
        """
        from optimed.core.domain import DocumentMetadata, DocumentFormat

        try:
            attachment = doc_ref.content[0].attachment if doc_ref.content else None
            content = self._extract_content_from_document_reference(doc_ref)

            return DocumentMetadata(
                file_name=attachment.title if attachment and hasattr(attachment, 'title') and attachment.title else f"document_{doc_ref.id}.txt",
                file_size=len(attachment.data) if attachment and hasattr(attachment, 'data') and attachment.data else len(content),
                format=self._map_document_format(attachment) if attachment else DocumentFormat.TXT,
                mime_type=attachment.contentType if attachment and hasattr(attachment, 'contentType') else "text/plain",
                word_count=len(content.split()) if content else 0,
                created_at=self._parse_document_date(doc_ref),
                title=self._extract_document_title(doc_ref)
            )

        except Exception as e:
            print(f"Error creating metadata: {e}")
            from optimed.core.domain import DocumentMetadata, DocumentFormat
            return DocumentMetadata(
                file_name=f"document_{doc_ref.id}.txt",
                file_size=0,
                format=DocumentFormat.TXT,
                mime_type="text/plain",
                word_count=0,
                created_at=datetime.now(timezone.utc),
                title="Unknown Document"
            )

    def _map_document_format(self, attachment):
        """Map FHIR attachment content type to DocumentFormat."""
        from optimed.core.domain import DocumentFormat

        if not hasattr(attachment, 'contentType') or not attachment.contentType:
            return DocumentFormat.TXT

        content_type = attachment.contentType.lower()
        if "pdf" in content_type:
            return DocumentFormat.PDF
        elif "html" in content_type:
            return DocumentFormat.HTML
        elif "json" in content_type:
            return DocumentFormat.JSON
        elif "markdown" in content_type:
            return DocumentFormat.MARKDOWN
        elif "word" in content_type or "docx" in content_type:
            return DocumentFormat.DOCX
        else:
            return DocumentFormat.TXT

    def _parse_document_date(self, doc_ref: DocumentReference) -> datetime:
        """
        Parse a date from DocumentReference.

        Args:
            doc_ref: The FHIR DocumentReference resource.

        Returns:
            A datetime object or current time if parsing fails.
        """
        try:
            if hasattr(doc_ref, 'date') and doc_ref.date:
                # Handle both string and datetime objects
                if isinstance(doc_ref.date, str):
                    return datetime.fromisoformat(doc_ref.date.replace('Z', '+00:00')).replace(tzinfo=timezone.utc)
                elif hasattr(doc_ref.date, 'replace'):
                    return doc_ref.date.replace(tzinfo=timezone.utc)

            # Fallback to current time
            return datetime.now(timezone.utc)

        except Exception as e:
            print(f"Error parsing document date: {e}")
            return datetime.now(timezone.utc)

    def _create_fallback_clinical_notes(self, patient_id: str) -> list:
        """
        Create fallback mock clinical notes when no real documents are found.

        Args:
            patient_id: The patient ID.

        Returns:
            List of mock JournalEntry objects.
        """
        from optimed.core.domain import JournalEntry, JournalType, ProcessingStatus, DocumentMetadata, DocumentFormat
        import uuid

        mock_notes = [
            {
                "id": f"fallback_doc_{uuid.uuid4().hex[:8]}",
                "title": "Discharge Summary - Hypertension Management",
                "content": "Patient discharged with improved blood pressure control. Continue ACE inhibitor therapy and follow up in 2 weeks. Blood pressure at discharge: 130/80 mmHg.",
                "type": JournalType.DISCHARGE_SUMMARY,
                "date": datetime.now(timezone.utc)
            },
            {
                "id": f"fallback_doc_{uuid.uuid4().hex[:8]}",
                "title": "Progress Note - Daily Assessment",
                "content": "Patient shows good response to current treatment regimen. Blood pressure stable. No adverse effects noted. Continue current medications.",
                "type": JournalType.PROGRESS_NOTE,
                "date": datetime.now(timezone.utc)
            },
            {
                "id": f"fallback_report_{uuid.uuid4().hex[:8]}",
                "title": "Laboratory Report - Lipid Panel",
                "content": "Total cholesterol: 185 mg/dL (Normal), LDL: 110 mg/dL (Borderline), HDL: 45 mg/dL (Low), Triglycerides: 150 mg/dL (Normal). Recommend dietary modifications.",
                "type": JournalType.LAB_REPORT,
                "date": datetime.now(timezone.utc)
            }
        ]

        journal_entries = []
        for note_data in mock_notes:
            try:
                metadata = DocumentMetadata(
                    file_name=f"{note_data['id']}.txt",
                    file_size=len(note_data['content']),
                    format=DocumentFormat.TXT,
                    mime_type="text/plain",
                    word_count=len(note_data['content'].split()),
                    created_at=note_data['date'],
                    title=note_data['title']
                )

                journal_entry = JournalEntry(
                    journal_id=note_data['id'],
                    patient_id=patient_id,
                    title=note_data['title'],
                    content=note_data['content'],
                    journal_type=note_data['type'],
                    metadata=metadata,
                    processing_status=ProcessingStatus.COMPLETED,
                    uploaded_at=note_data['date'],
                    source="EHR"
                )

                journal_entries.append(journal_entry)

            except Exception as e:
                print(f"Error creating fallback journal entry: {e}")
                continue

        return journal_entries

    async def __aenter__(self):
        return self
    
    async def __aexit__(self, *exc):
        await self.close()

    