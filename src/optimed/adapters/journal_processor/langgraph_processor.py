from __future__ import annotations

import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Dict, Any

from langgraph.graph import StateGraph, END
from pydantic import BaseModel, Field

from optimed.core.domain import (
    JournalEntry, ProcessingResult, DocumentMetadata, JournalType, 
    ProcessingStatus, DocumentFormat
)
from optimed.core.ports import (
    JournalProcessor, DocumentParser, FormatDetector, 
    JournalInterpreter, JournalRepository, VectorStore
)
from optimed.embeddings.minilm import embed


class ProcessingState(BaseModel):
    """State for journal processing workflow."""
    
    # Input
    file_path: Optional[str] = None
    file_data: Optional[bytes] = None
    filename: Optional[str] = None
    patient_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    # Processing state
    processing_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    processing_started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Detected format and type
    detected_format: Optional[DocumentFormat] = None
    detected_journal_type: Optional[JournalType] = None
    
    # Parsed content
    extracted_content: Optional[str] = None
    document_metadata: Optional[DocumentMetadata] = None
    
    # Created entities
    journal_entry: Optional[JournalEntry] = None
    interpretation: Optional[Any] = None  # JournalInterpretation
    
    # Vector embedding
    embedding: Optional[list[float]] = None
    embedding_stored: bool = False
    
    # Results
    success: bool = True
    error_message: Optional[str] = None
    warnings: list[str] = Field(default_factory=list)


class LangGraphJournalProcessor(JournalProcessor):
    """LangGraph-based journal processing orchestrator."""

    def __init__(
        self,
        document_parser: DocumentParser,
        format_detector: FormatDetector,
        journal_interpreter: JournalInterpreter,
        journal_repository: JournalRepository,
        vector_store: VectorStore
    ):
        self.document_parser = document_parser
        self.format_detector = format_detector
        self.journal_interpreter = journal_interpreter
        self.journal_repository = journal_repository
        self.vector_store = vector_store
        self._compiled_graph = self._build_graph()

    def _build_graph(self) -> Any:
        """Build the LangGraph processing workflow."""
        
        async def detect_format(state: ProcessingState) -> ProcessingState:
            """Detect document format."""
            try:
                if state.file_path:
                    detected_format = await self.format_detector.detect_format(
                        state.file_path, state.filename
                    )
                elif state.file_data:
                    detected_format = await self.format_detector.detect_format(
                        state.file_data, state.filename
                    )
                else:
                    raise ValueError("No file path or data provided")
                
                state.detected_format = detected_format
                
            except Exception as e:
                state.success = False
                state.error_message = f"Format detection failed: {str(e)}"
            
            return state

        async def parse_document(state: ProcessingState) -> ProcessingState:
            """Parse document content."""
            if not state.success:
                return state
                
            try:
                if state.file_path:
                    content, metadata = await self.document_parser.parse(
                        state.file_path, state.detected_format
                    )
                elif state.file_data:
                    content, metadata = await self.document_parser.parse(
                        state.file_data, state.detected_format
                    )
                else:
                    raise ValueError("No file path or data provided")
                
                state.extracted_content = content
                state.document_metadata = metadata
                
                # Update format if it was detected during parsing
                if metadata.format != DocumentFormat.UNKNOWN:
                    state.detected_format = metadata.format
                
            except Exception as e:
                state.success = False
                state.error_message = f"Document parsing failed: {str(e)}"
            
            return state

        async def detect_journal_type(state: ProcessingState) -> ProcessingState:
            """Detect journal type from content."""
            if not state.success or not state.extracted_content:
                return state
                
            try:
                journal_type = await self.format_detector.detect_journal_type(
                    state.extracted_content, state.document_metadata
                )
                state.detected_journal_type = journal_type
                
            except Exception as e:
                state.warnings.append(f"Journal type detection failed: {str(e)}")
                state.detected_journal_type = JournalType.UNKNOWN
            
            return state

        async def create_journal_entry(state: ProcessingState) -> ProcessingState:
            """Create journal entry from parsed content."""
            if not state.success:
                return state
                
            try:
                journal_id = str(uuid.uuid4())
                
                # Extract title from content or use filename
                title = state.document_metadata.title or state.filename or "Untitled Document"
                
                journal_entry = JournalEntry(
                    journal_id=journal_id,
                    patient_id=state.patient_id,
                    user_id=state.user_id,
                    session_id=state.session_id,
                    title=title,
                    content=state.extracted_content,
                    journal_type=state.detected_journal_type or JournalType.UNKNOWN,
                    metadata=state.document_metadata,
                    processing_status=ProcessingStatus.PARSING,
                )
                
                state.journal_entry = journal_entry
                
            except Exception as e:
                state.success = False
                state.error_message = f"Journal entry creation failed: {str(e)}"
            
            return state

        async def interpret_content(state: ProcessingState) -> ProcessingState:
            """Generate AI interpretation of journal content."""
            if not state.success or not state.journal_entry:
                return state
                
            try:
                # Update status to interpreting
                state.journal_entry = state.journal_entry.model_copy(
                    update={"processing_status": ProcessingStatus.INTERPRETING}
                )
                
                interpretation = await self.journal_interpreter.interpret(state.journal_entry)
                state.interpretation = interpretation
                
            except Exception as e:
                state.warnings.append(f"Content interpretation failed: {str(e)}")
                # Continue processing without interpretation
            
            return state

        async def generate_embedding(state: ProcessingState) -> ProcessingState:
            """Generate vector embedding for the journal."""
            if not state.success or not state.journal_entry:
                return state
                
            try:
                # Update status to embedding
                state.journal_entry = state.journal_entry.model_copy(
                    update={"processing_status": ProcessingStatus.EMBEDDING}
                )
                
                # Create text for embedding (title + abstract + content sample)
                embedding_text = state.journal_entry.title
                if state.journal_entry.abstract:
                    embedding_text += f"\n\n{state.journal_entry.abstract}"
                
                # Add content sample (first 2000 chars to avoid token limits)
                content_sample = state.journal_entry.content[:2000]
                embedding_text += f"\n\n{content_sample}"
                
                # Generate embedding
                embedding = await embed(embedding_text)
                state.embedding = embedding
                
            except Exception as e:
                state.warnings.append(f"Embedding generation failed: {str(e)}")
                # Continue processing without embedding
            
            return state

        async def store_data(state: ProcessingState) -> ProcessingState:
            """Store journal entry, interpretation, and embedding."""
            if not state.success or not state.journal_entry:
                return state
                
            try:
                # Update status to completed
                state.journal_entry = state.journal_entry.model_copy(update={
                    "processing_status": ProcessingStatus.COMPLETED,
                    "processed_at": datetime.now(timezone.utc)
                })
                
                # Store journal entry
                await self.journal_repository.store_journal(state.journal_entry)
                
                # Store interpretation if available
                if state.interpretation:
                    await self.journal_repository.store_interpretation(state.interpretation)
                
                # Store embedding if available
                if state.embedding:
                    await self.vector_store.store_journal_embedding(
                        state.journal_entry,
                        state.embedding,
                        state.interpretation
                    )
                    state.embedding_stored = True
                
            except Exception as e:
                state.success = False
                state.error_message = f"Data storage failed: {str(e)}"
            
            return state

        # Build the graph
        builder = StateGraph(ProcessingState)
        
        # Add nodes
        builder.add_node("detect_format", detect_format)
        builder.add_node("parse_document", parse_document)
        builder.add_node("detect_journal_type", detect_journal_type)
        builder.add_node("create_journal_entry", create_journal_entry)
        builder.add_node("interpret_content", interpret_content)
        builder.add_node("generate_embedding", generate_embedding)
        builder.add_node("store_data", store_data)
        
        # Set entry point
        builder.set_entry_point("detect_format")
        
        # Add edges
        builder.add_edge("detect_format", "parse_document")
        builder.add_edge("parse_document", "detect_journal_type")
        builder.add_edge("detect_journal_type", "create_journal_entry")
        builder.add_edge("create_journal_entry", "interpret_content")
        builder.add_edge("interpret_content", "generate_embedding")
        builder.add_edge("generate_embedding", "store_data")
        builder.add_edge("store_data", END)
        
        return builder.compile()

    async def process_document(
        self, 
        file_path: str | bytes, 
        filename: Optional[str] = None,
        patient_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> ProcessingResult:
        """Process a document end-to-end."""
        
        processing_started_at = datetime.now(timezone.utc)
        
        # Prepare initial state
        if isinstance(file_path, str):
            initial_state = ProcessingState(
                file_path=file_path,
                filename=filename or Path(file_path).name,
                patient_id=patient_id,
                user_id=user_id,
                session_id=session_id
            )
        else:
            initial_state = ProcessingState(
                file_data=file_path,
                filename=filename or "unknown",
                patient_id=patient_id,
                user_id=user_id,
                session_id=session_id
            )
        
        # Run the processing workflow
        final_state = await self._compiled_graph.ainvoke(initial_state)
        
        processing_completed_at = datetime.now(timezone.utc)
        total_time = (processing_completed_at - processing_started_at).total_seconds()
        
        # Create processing result
        return ProcessingResult(
            result_id=final_state.processing_id,
            journal_entry=final_state.journal_entry,
            interpretation=final_state.interpretation,
            processing_started_at=processing_started_at,
            processing_completed_at=processing_completed_at,
            total_processing_time_seconds=total_time,
            success=final_state.success,
            error_message=final_state.error_message,
            warnings=final_state.warnings,
            embedding_stored=final_state.embedding_stored
        )

    async def reprocess_journal(self, journal_id: str) -> ProcessingResult:
        """Reprocess an existing journal with updated AI models."""
        
        # Get existing journal
        journal_entry = await self.journal_repository.get_journal(journal_id)
        if not journal_entry:
            raise ValueError(f"Journal not found: {journal_id}")
        
        processing_started_at = datetime.now(timezone.utc)
        
        try:
            # Re-interpret the content
            interpretation = await self.journal_interpreter.interpret(journal_entry)
            
            # Store updated interpretation
            await self.journal_repository.store_interpretation(interpretation)
            
            # Update vector embedding with new interpretation
            if journal_entry.content:
                embedding_text = f"{journal_entry.title}\n\n{journal_entry.content[:2000]}"
                embedding = await embed(embedding_text)
                
                await self.vector_store.store_journal_embedding(
                    journal_entry, embedding, interpretation
                )
            
            processing_completed_at = datetime.now(timezone.utc)
            total_time = (processing_completed_at - processing_started_at).total_seconds()
            
            return ProcessingResult(
                result_id=str(uuid.uuid4()),
                journal_entry=journal_entry,
                interpretation=interpretation,
                processing_started_at=processing_started_at,
                processing_completed_at=processing_completed_at,
                total_processing_time_seconds=total_time,
                success=True,
                embedding_stored=True
            )
            
        except Exception as e:
            processing_completed_at = datetime.now(timezone.utc)
            total_time = (processing_completed_at - processing_started_at).total_seconds()
            
            return ProcessingResult(
                result_id=str(uuid.uuid4()),
                journal_entry=journal_entry,
                processing_started_at=processing_started_at,
                processing_completed_at=processing_completed_at,
                total_processing_time_seconds=total_time,
                success=False,
                error_message=f"Reprocessing failed: {str(e)}"
            )
