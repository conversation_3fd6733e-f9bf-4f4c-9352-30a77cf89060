'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Upload, Search, FileText, Calendar, User, TrendingUp } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Journal {
  journal_id: string;
  title: string;
  journal_type: string;
  patient_id?: string;
  user_id?: string;
  processing_status: string;
  uploaded_at: string;
  word_count?: number;
  has_interpretation: boolean;
}

interface JournalSearchResult {
  journals: Journal[];
  total: number;
}

export default function JournalsPage() {
  const [journals, setJournals] = useState<Journal[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    loadJournals();
  }, []);

  const loadJournals = async () => {
    try {
      setLoading(true);
      // In a real implementation, this would call your API
      const response = await fetch('/api/journals/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: '', limit: 20 })
      });
      
      if (response.ok) {
        const data = await response.json();
        setJournals(data);
      } else {
        // Mock data for demonstration
        setJournals([
          {
            journal_id: 'journal_001',
            title: 'ACE Inhibitors in Elderly Hypertension',
            journal_type: 'RESEARCH_PAPER',
            patient_id: 'patient_123',
            processing_status: 'COMPLETED',
            uploaded_at: '2024-01-15T10:30:00Z',
            word_count: 2500,
            has_interpretation: true
          },
          {
            journal_id: 'journal_002', 
            title: 'Case Study: Resistant Hypertension Management',
            journal_type: 'CASE_STUDY',
            patient_id: 'patient_123',
            processing_status: 'COMPLETED',
            uploaded_at: '2024-01-10T14:20:00Z',
            word_count: 1200,
            has_interpretation: true
          },
          {
            journal_id: 'journal_003',
            title: 'Diabetes Management Guidelines 2024',
            journal_type: 'CLINICAL_GUIDELINE',
            processing_status: 'COMPLETED',
            uploaded_at: '2024-01-08T09:15:00Z',
            word_count: 3200,
            has_interpretation: true
          }
        ]);
      }
    } catch (error) {
      console.error('Error loading journals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadJournals();
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/journals/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery, limit: 20 })
      });
      
      if (response.ok) {
        const data = await response.json();
        setJournals(data);
      }
    } catch (error) {
      console.error('Error searching journals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = () => {
    router.push('/journals/upload');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'PROCESSING': return 'bg-yellow-100 text-yellow-800';
      case 'FAILED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'RESEARCH_PAPER': return 'bg-blue-100 text-blue-800';
      case 'CASE_STUDY': return 'bg-purple-100 text-purple-800';
      case 'CLINICAL_GUIDELINE': return 'bg-green-100 text-green-800';
      case 'LAB_REPORT': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Medical Journals</h1>
          <p className="text-gray-600 mt-1">
            Manage and search your medical literature and case studies
          </p>
        </div>
        <Button onClick={handleUpload} className="flex items-center gap-2">
          <Upload className="h-4 w-4" />
          Upload Journal
        </Button>
      </div>

      {/* Search Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search journals by title, content, or medical condition..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} variant="outline" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Journals</p>
                <p className="text-2xl font-bold">{journals.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">With AI Analysis</p>
                <p className="text-2xl font-bold">
                  {journals.filter(j => j.has_interpretation).length}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Patient-Linked</p>
                <p className="text-2xl font-bold">
                  {journals.filter(j => j.patient_id).length}
                </p>
              </div>
              <User className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold">
                  {journals.filter(j => {
                    const uploadDate = new Date(j.uploaded_at);
                    const now = new Date();
                    return uploadDate.getMonth() === now.getMonth() && 
                           uploadDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Journals List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading journals...</p>
          </div>
        ) : journals.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No journals found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search terms' : 'Upload your first medical journal to get started'}
              </p>
              {!searchQuery && (
                <Button onClick={handleUpload}>Upload Journal</Button>
              )}
            </CardContent>
          </Card>
        ) : (
          journals.map((journal) => (
            <Card key={journal.journal_id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{journal.title}</CardTitle>
                    <CardDescription className="flex items-center gap-4 text-sm">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(journal.uploaded_at)}
                      </span>
                      {journal.word_count && (
                        <span>{journal.word_count.toLocaleString()} words</span>
                      )}
                      {journal.patient_id && (
                        <span className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          Patient: {journal.patient_id}
                        </span>
                      )}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Badge className={getStatusColor(journal.processing_status)}>
                      {journal.processing_status}
                    </Badge>
                    <Badge className={getTypeColor(journal.journal_type)}>
                      {journal.journal_type.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-4">
                    {journal.has_interpretation && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        ✓ AI Analyzed
                      </Badge>
                    )}
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/journals/${journal.journal_id}`)}
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
