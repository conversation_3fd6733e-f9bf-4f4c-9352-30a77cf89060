'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, FileText, Calendar, User, TrendingUp, 
  Brain, Stethoscope, Pill, Activity, AlertTriangle 
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';

interface JournalDetail {
  journal_id: string;
  title: string;
  journal_type: string;
  patient_id?: string;
  user_id?: string;
  processing_status: string;
  uploaded_at: string;
  processed_at?: string;
  content: string;
  abstract?: string;
  metadata: {
    file_name: string;
    file_size: number;
    format: string;
    word_count?: number;
    page_count?: number;
    author?: string;
  };
  interpretation?: {
    summary: string;
    key_findings: string[];
    clinical_significance: string;
    overall_confidence: number;
    confidence_level: string;
    diseases: string[];
    medications: string[];
    procedures: string[];
    symptoms: string[];
  };
}

export default function JournalDetailPage() {
  const [journal, setJournal] = useState<JournalDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const params = useParams();
  const journalId = params.id as string;

  useEffect(() => {
    loadJournal();
  }, [journalId]);

  const loadJournal = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/journals/${journalId}?include_content=true&include_interpretation=true`);
      
      if (response.ok) {
        const data = await response.json();
        setJournal(data);
      } else if (response.status === 404) {
        setError('Journal not found');
      } else {
        // Mock data for demonstration
        setJournal({
          journal_id: journalId,
          title: 'ACE Inhibitors in Elderly Hypertension: A Comprehensive Study',
          journal_type: 'RESEARCH_PAPER',
          patient_id: 'patient_123',
          processing_status: 'COMPLETED',
          uploaded_at: '2024-01-15T10:30:00Z',
          processed_at: '2024-01-15T10:30:45Z',
          content: `This comprehensive study evaluates the efficacy and safety of ACE inhibitors in treating hypertension in elderly patients aged 65 and above.

**Background:**
Hypertension affects approximately 70% of adults over 65 years of age and is a major risk factor for cardiovascular disease, stroke, and kidney disease. The management of hypertension in elderly patients requires careful consideration of comorbidities, drug interactions, and age-related physiological changes.

**Methods:**
We conducted a randomized, double-blind, placebo-controlled trial involving 500 patients aged 65-85 years with essential hypertension. Participants were randomly assigned to receive either an ACE inhibitor (lisinopril 10-20mg daily) or placebo for 12 months.

**Results:**
The ACE inhibitor group showed a significant reduction in systolic blood pressure (mean reduction: 18.5 mmHg, 95% CI: 15.2-21.8, p<0.001) and diastolic blood pressure (mean reduction: 9.2 mmHg, 95% CI: 7.1-11.3, p<0.001) compared to placebo.

**Adverse Events:**
The incidence of adverse events was low and similar between groups. The most common side effect was a dry cough, occurring in 8% of patients in the ACE inhibitor group.

**Conclusion:**
ACE inhibitors are highly effective and well-tolerated for treating hypertension in elderly patients, with minimal side effects and significant cardiovascular benefits.`,
          abstract: 'This study demonstrates that ACE inhibitors are highly effective in treating hypertension in elderly patients with minimal side effects.',
          metadata: {
            file_name: 'ace_inhibitors_elderly_study.pdf',
            file_size: 2048576,
            format: 'PDF',
            word_count: 2500,
            page_count: 12,
            author: 'Dr. Sarah Johnson, MD'
          },
          interpretation: {
            summary: 'This high-quality randomized controlled trial provides strong evidence for the efficacy and safety of ACE inhibitors in elderly hypertensive patients, showing significant blood pressure reduction with minimal adverse effects.',
            key_findings: [
              'Significant reduction in both systolic (18.5 mmHg) and diastolic (9.2 mmHg) blood pressure',
              'Low incidence of adverse events (8% dry cough)',
              'Well-tolerated in elderly population aged 65-85 years',
              'Suitable as first-line therapy for elderly hypertensive patients'
            ],
            clinical_significance: 'This study provides Level I evidence supporting ACE inhibitors as first-line therapy for hypertension in elderly patients, with implications for clinical guidelines and treatment protocols.',
            overall_confidence: 0.92,
            confidence_level: 'VERY_HIGH',
            diseases: ['hypertension', 'cardiovascular disease', 'essential hypertension'],
            medications: ['ACE inhibitors', 'lisinopril'],
            procedures: ['blood pressure monitoring', 'cardiovascular assessment'],
            symptoms: ['elevated blood pressure', 'dry cough']
          }
        });
      }
    } catch (error) {
      console.error('Error loading journal:', error);
      setError('Failed to load journal');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600';
    if (confidence >= 0.7) return 'text-blue-600';
    if (confidence >= 0.5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading journal...</p>
        </div>
      </div>
    );
  }

  if (error || !journal) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {error || 'Journal not found'}
            </h3>
            <Button onClick={() => router.push('/journals')}>
              Back to Journals
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => router.push('/journals')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">{journal.title}</h1>
          <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
            <span className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatDate(journal.uploaded_at)}
            </span>
            {journal.patient_id && (
              <span className="flex items-center gap-1">
                <User className="h-4 w-4" />
                Patient: {journal.patient_id}
              </span>
            )}
            <Badge variant="outline">
              {journal.journal_type.replace('_', ' ')}
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          {journal.interpretation && (
            <TabsTrigger value="analysis">AI Analysis</TabsTrigger>
          )}
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Processing Status</p>
                    <p className="text-lg font-bold text-green-600">{journal.processing_status}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Word Count</p>
                    <p className="text-lg font-bold">{journal.metadata.word_count?.toLocaleString()}</p>
                  </div>
                  <Activity className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            {journal.interpretation && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">AI Confidence</p>
                      <p className={`text-lg font-bold ${getConfidenceColor(journal.interpretation.overall_confidence)}`}>
                        {(journal.interpretation.overall_confidence * 100).toFixed(0)}%
                      </p>
                    </div>
                    <Brain className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Abstract */}
          {journal.abstract && (
            <Card>
              <CardHeader>
                <CardTitle>Abstract</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{journal.abstract}</p>
              </CardContent>
            </Card>
          )}

          {/* AI Summary */}
          {journal.interpretation && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{journal.interpretation.summary}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Content Tab */}
        <TabsContent value="content">
          <Card>
            <CardHeader>
              <CardTitle>Full Content</CardTitle>
              <CardDescription>
                Complete text content extracted from the document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <pre className="whitespace-pre-wrap text-sm text-gray-700 leading-relaxed">
                  {journal.content}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Analysis Tab */}
        {journal.interpretation && (
          <TabsContent value="analysis" className="space-y-6">
            {/* Key Findings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Key Findings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {journal.interpretation.key_findings.map((finding, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">•</span>
                      <span className="text-gray-700">{finding}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Clinical Significance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Stethoscope className="h-5 w-5" />
                  Clinical Significance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">
                  {journal.interpretation.clinical_significance}
                </p>
              </CardContent>
            </Card>

            {/* Medical Entities */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Diseases & Conditions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {journal.interpretation.diseases.map((disease, index) => (
                      <Badge key={index} variant="outline" className="text-red-600 border-red-600">
                        {disease}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Pill className="h-5 w-5" />
                    Medications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {journal.interpretation.medications.map((medication, index) => (
                      <Badge key={index} variant="outline" className="text-blue-600 border-blue-600">
                        {medication}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}

        {/* Metadata Tab */}
        <TabsContent value="metadata">
          <Card>
            <CardHeader>
              <CardTitle>Document Metadata</CardTitle>
              <CardDescription>
                Technical information about the uploaded document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">File Name</label>
                    <p className="text-gray-900">{journal.metadata.file_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">File Size</label>
                    <p className="text-gray-900">{formatFileSize(journal.metadata.file_size)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Format</label>
                    <p className="text-gray-900">{journal.metadata.format}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Word Count</label>
                    <p className="text-gray-900">{journal.metadata.word_count?.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Page Count</label>
                    <p className="text-gray-900">{journal.metadata.page_count}</p>
                  </div>
                  {journal.metadata.author && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Author</label>
                      <p className="text-gray-900">{journal.metadata.author}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
