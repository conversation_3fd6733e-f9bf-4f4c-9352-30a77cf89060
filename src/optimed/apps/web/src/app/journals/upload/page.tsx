'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Upload, FileText, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface UploadResult {
  success: boolean;
  journal_id?: string;
  error_message?: string;
  processing_time_seconds?: number;
}

export default function UploadJournalPage() {
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState('');
  const [journalType, setJournalType] = useState('');
  const [patientId, setPatientId] = useState('');
  const [description, setDescription] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const router = useRouter();

  const journalTypes = [
    { value: 'RESEARCH_PAPER', label: 'Research Paper' },
    { value: 'CASE_STUDY', label: 'Case Study' },
    { value: 'CLINICAL_TRIAL', label: 'Clinical Trial' },
    { value: 'REVIEW_ARTICLE', label: 'Review Article' },
    { value: 'CLINICAL_GUIDELINE', label: 'Clinical Guideline' },
    { value: 'LAB_REPORT', label: 'Lab Report' },
    { value: 'IMAGING_REPORT', label: 'Imaging Report' },
    { value: 'DISCHARGE_SUMMARY', label: 'Discharge Summary' },
    { value: 'PROGRESS_NOTE', label: 'Progress Note' }
  ];

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      // Auto-populate title from filename if not already set
      if (!title) {
        const fileName = selectedFile.name.replace(/\.[^/.]+$/, ''); // Remove extension
        setTitle(fileName.replace(/[_-]/g, ' ')); // Replace underscores/hyphens with spaces
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      alert('Please select a file to upload');
      return;
    }

    setUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      if (title) formData.append('title', title);
      if (journalType) formData.append('journal_type', journalType);
      if (patientId) formData.append('patient_id', patientId);

      const response = await fetch('/api/journals/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (response.ok) {
        setUploadResult({
          success: true,
          journal_id: result.journal?.journal_id,
          processing_time_seconds: result.processing_time_seconds
        });
      } else {
        setUploadResult({
          success: false,
          error_message: result.detail || 'Upload failed'
        });
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadResult({
        success: false,
        error_message: 'Network error occurred during upload'
      });
    } finally {
      setUploading(false);
    }
  };

  const resetForm = () => {
    setFile(null);
    setTitle('');
    setJournalType('');
    setPatientId('');
    setDescription('');
    setUploadResult(null);
  };

  const getSupportedFormats = () => {
    return 'PDF, DOCX, DOC, TXT, RTF, JSON, XML, HTML, Markdown';
  };

  const getMaxFileSize = () => {
    return '50 MB';
  };

  if (uploadResult?.success) {
    return (
      <div className="container mx-auto p-6 max-w-2xl">
        <Card>
          <CardContent className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Upload Successful!</h2>
            <p className="text-gray-600 mb-6">
              Your journal has been uploaded and is being processed by our AI system.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Journal ID:</span>
                  <p className="text-gray-600">{uploadResult.journal_id}</p>
                </div>
                <div>
                  <span className="font-medium">Processing Time:</span>
                  <p className="text-gray-600">
                    {uploadResult.processing_time_seconds?.toFixed(1)}s
                  </p>
                </div>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <Button 
                onClick={() => router.push(`/journals/${uploadResult.journal_id}`)}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                View Journal
              </Button>
              <Button variant="outline" onClick={resetForm}>
                Upload Another
              </Button>
              <Button variant="outline" onClick={() => router.push('/journals')}>
                Back to Journals
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => router.push('/journals')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Upload Medical Journal</h1>
          <p className="text-gray-600 mt-1">
            Upload medical literature for AI analysis and integration
          </p>
        </div>
      </div>

      {/* Upload Form */}
      <Card>
        <CardHeader>
          <CardTitle>Journal Information</CardTitle>
          <CardDescription>
            Provide details about the medical journal or document you're uploading
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file">Document File *</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              <input
                id="file"
                type="file"
                onChange={handleFileChange}
                accept=".pdf,.docx,.doc,.txt,.rtf,.json,.xml,.html,.md"
                className="hidden"
              />
              <label htmlFor="file" className="cursor-pointer">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm font-medium text-gray-900">
                  {file ? file.name : 'Click to upload or drag and drop'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Supported formats: {getSupportedFormats()}
                </p>
                <p className="text-xs text-gray-500">
                  Maximum file size: {getMaxFileSize()}
                </p>
              </label>
            </div>
            {file && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                File selected: {file.name} ({(file.size / 1024 / 1024).toFixed(1)} MB)
              </div>
            )}
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter journal title (auto-detected from filename)"
            />
          </div>

          {/* Journal Type */}
          <div className="space-y-2">
            <Label htmlFor="journal-type">Journal Type</Label>
            <Select value={journalType} onValueChange={setJournalType}>
              <SelectTrigger>
                <SelectValue placeholder="Select journal type (auto-detected if not specified)" />
              </SelectTrigger>
              <SelectContent>
                {journalTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Patient ID */}
          <div className="space-y-2">
            <Label htmlFor="patient-id">Patient ID (Optional)</Label>
            <Input
              id="patient-id"
              value={patientId}
              onChange={(e) => setPatientId(e.target.value)}
              placeholder="Link this journal to a specific patient"
            />
            <p className="text-xs text-gray-500">
              Leave empty for general medical knowledge that applies to all patients
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Additional notes about this journal..."
              rows={3}
            />
          </div>

          {/* Error Display */}
          {uploadResult?.error_message && (
            <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <div>
                <p className="font-medium text-red-800">Upload Failed</p>
                <p className="text-sm text-red-600">{uploadResult.error_message}</p>
              </div>
            </div>
          )}

          {/* Upload Button */}
          <div className="flex gap-4">
            <Button 
              onClick={handleUpload} 
              disabled={!file || uploading}
              className="flex-1 flex items-center gap-2"
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4" />
                  Upload & Process
                </>
              )}
            </Button>
            <Button variant="outline" onClick={resetForm} disabled={uploading}>
              Reset
            </Button>
          </div>

          {/* Processing Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">What happens after upload?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Document format and type are automatically detected</li>
              <li>• Text content is extracted and analyzed</li>
              <li>• AI generates medical insights and interpretations</li>
              <li>• Content is indexed for semantic search</li>
              <li>• Journal becomes available for diagnostic consultations</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
