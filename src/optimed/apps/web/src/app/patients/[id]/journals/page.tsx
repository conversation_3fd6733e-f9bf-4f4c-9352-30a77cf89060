'use client';

import { ProtectedRoute } from '@/components/ProtectedRoute';
import { Header } from '@/components/Header';
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { fetchPatient, PatientDetail } from "@/lib/api";
import { useUser } from '@/contexts/UserContext';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Brain, Calendar, User, ArrowLeft, ExternalLink, Eye, Download } from "lucide-react";

interface Journal {
  journal_id: string;
  title: string;
  journal_type: string;
  patient_id?: string;
  user_id?: string;
  processing_status: string;
  uploaded_at: string;
  processed_at?: string;
  word_count?: number;
  has_interpretation: boolean;
}

function PatientJournalsContent() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const [patient, setPatient] = useState<PatientDetail | null>(null);
  const [journals, setJournals] = useState<Journal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const patientId = params?.id as string;

  useEffect(() => {
    const loadData = async () => {
      if (!patientId) return;

      setLoading(true);
      setError(null);
      try {
        // Load patient details
        const patientResult = await fetchPatient(patientId);
        setPatient(patientResult);

        // Load patient journals
        const journalsResponse = await fetch(`/api/patients/${patientId}/journals`, {
          headers: {
            'X-Session-ID': localStorage.getItem('session_id') || ''
          }
        });

        if (journalsResponse.ok) {
          const journalsData = await journalsResponse.json();
          setJournals(journalsData);
        } else {
          // Mock data for demonstration
          setJournals([
            {
              journal_id: 'journal_001',
              title: 'ACE Inhibitors in Elderly Hypertension: A Comprehensive Study',
              journal_type: 'RESEARCH_PAPER',
              patient_id: patientId,
              processing_status: 'COMPLETED',
              uploaded_at: '2024-01-15T10:30:00Z',
              processed_at: '2024-01-15T10:30:45Z',
              word_count: 2500,
              has_interpretation: true
            },
            {
              journal_id: 'fhir_doc_12345',
              title: 'Discharge Summary - Hypertension Management',
              journal_type: 'CLINICAL_NOTE',
              patient_id: patientId,
              processing_status: 'COMPLETED',
              uploaded_at: '2024-01-10T14:20:00Z',
              processed_at: '2024-01-10T14:25:00Z',
              word_count: 800,
              has_interpretation: true
            },
            {
              journal_id: 'fhir_report_67890',
              title: 'Laboratory Report - Lipid Panel',
              journal_type: 'LAB_REPORT',
              patient_id: patientId,
              processing_status: 'COMPLETED',
              uploaded_at: '2024-01-08T09:15:00Z',
              processed_at: '2024-01-08T09:20:00Z',
              word_count: 300,
              has_interpretation: true
            }
          ]);
        }
      } catch (err: any) {
        setError(err.message || "Failed to load data");
        console.error('Load error:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [patientId]);

  const getJournalTypeColor = (type: string) => {
    switch (type) {
      case 'RESEARCH_PAPER': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'CASE_STUDY': return 'bg-green-100 text-green-800 border-green-200';
      case 'CLINICAL_NOTE': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'LAB_REPORT': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CLINICAL_GUIDELINE': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'bg-green-100 text-green-800';
      case 'PROCESSING': return 'bg-yellow-100 text-yellow-800';
      case 'PENDING': return 'bg-gray-100 text-gray-800';
      case 'FAILED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading patient journals...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600 text-lg">{error}</p>
            <Button 
              onClick={() => router.back()} 
              className="mt-4"
              variant="outline"
            >
              Go Back
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Breadcrumb */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/patients" className="text-blue-600 hover:text-blue-800">
                  Patients
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li className="inline-flex items-center">
                <Link href={`/patients/${patientId}`} className="text-blue-600 hover:text-blue-800">
                  {patient?.name}
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Journals</span>
              </li>
            </ol>
          </nav>

          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Medical Journals</h1>
                <p className="text-gray-600">Patient: {patient?.name}</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => router.push(`/journals/upload?patient_id=${patientId}`)}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Upload Journal
              </Button>
            </div>
          </div>

          {/* Journals List */}
          <div className="space-y-4">
            {journals.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No journals found</h3>
                  <p className="text-gray-600 mb-4">
                    No medical journals have been uploaded for this patient yet.
                  </p>
                  <Button
                    onClick={() => router.push(`/journals/upload?patient_id=${patientId}`)}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    Upload First Journal
                  </Button>
                </CardContent>
              </Card>
            ) : (
              journals.map((journal) => (
                <Card key={journal.journal_id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{journal.title}</h3>
                          <Badge className={getJournalTypeColor(journal.journal_type)}>
                            {journal.journal_type.replace('_', ' ')}
                          </Badge>
                          <Badge className={getStatusColor(journal.processing_status)}>
                            {journal.processing_status}
                          </Badge>
                          {journal.has_interpretation && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              <Brain className="h-3 w-3 mr-1" />
                              AI Analyzed
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-6 text-sm text-gray-600 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {new Date(journal.uploaded_at).toLocaleDateString()}
                          </div>
                          {journal.word_count && (
                            <div>{journal.word_count.toLocaleString()} words</div>
                          )}
                          {journal.journal_id.startsWith('fhir_') && (
                            <Badge variant="outline" className="text-blue-600 border-blue-600">
                              From EHR
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          onClick={() => router.push(`/journals/${journal.journal_id}`)}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          View
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  );
}

export default function PatientJournalsPage() {
  return (
    <ProtectedRoute>
      <PatientJournalsContent />
    </ProtectedRoute>
  );
}
