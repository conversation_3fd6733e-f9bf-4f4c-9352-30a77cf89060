'use client';

import { ProtectedRoute } from '@/components/ProtectedRoute';
import { Header } from '@/components/Header';
import { JournalNotes } from '@/components/JournalNotes';
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { fetchPatient, PatientDetail } from "@/lib/api";
import { useUser } from '@/contexts/UserContext';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, FileText, Database, TrendingUp, Clock } from "lucide-react";

interface JournalSummary {
  patient_id: string;
  summary: {
    total_journals: number;
    journal_types: Record<string, number>;
    key_topics: string[];
    last_updated: string | null;
  };
  recent_notes: any[];
  statistics: {
    ehr_sourced: number;
    uploaded: number;
    with_interpretation: number;
  };
}

function JournalNotesPageContent() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const [patient, setPatient] = useState<PatientDetail | null>(null);
  const [journalSummary, setJournalSummary] = useState<JournalSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const patientId = params?.id as string;

  useEffect(() => {
    const loadData = async () => {
      if (!patientId) return;

      setLoading(true);
      setError(null);
      try {
        // Load patient details
        const patientResult = await fetchPatient(patientId);
        setPatient(patientResult);

        // Load journal summary
        const summaryResponse = await fetch(`/api/patients/${patientId}/journal-summary`, {
          headers: {
            'X-Session-ID': localStorage.getItem('session_id') || ''
          }
        });

        if (summaryResponse.ok) {
          const summaryData = await summaryResponse.json();
          setJournalSummary(summaryData);
        } else {
          // Mock data for demonstration
          setJournalSummary({
            patient_id: patientId,
            summary: {
              total_journals: 8,
              journal_types: {
                'DISCHARGE_SUMMARY': 2,
                'PROGRESS_NOTE': 4,
                'LAB_REPORT': 2
              },
              key_topics: ['hypertension', 'medication', 'blood pressure', 'follow-up'],
              last_updated: new Date().toISOString()
            },
            recent_notes: [],
            statistics: {
              ehr_sourced: 6,
              uploaded: 2,
              with_interpretation: 5
            }
          });
        }
      } catch (err: any) {
        setError(err.message || "Failed to load data");
        console.error('Load error:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [patientId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading journal notes...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600 text-lg">{error}</p>
            <Button 
              onClick={() => router.back()} 
              className="mt-4"
              variant="outline"
            >
              Go Back
            </Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Breadcrumb */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/patients" className="text-blue-600 hover:text-blue-800">
                  Patients
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li className="inline-flex items-center">
                <Link href={`/patients/${patientId}`} className="text-blue-600 hover:text-blue-800">
                  {patient?.name}
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <span className="text-gray-900 font-medium">Journal Notes</span>
              </li>
            </ol>
          </nav>

          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Journal Notes</h1>
                <p className="text-gray-600">Patient: {patient?.name}</p>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          {journalSummary && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Notes</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {journalSummary.summary.total_journals}
                      </p>
                    </div>
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">From EHR</p>
                      <p className="text-2xl font-bold text-green-600">
                        {journalSummary.statistics.ehr_sourced}
                      </p>
                    </div>
                    <Database className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">AI Analyzed</p>
                      <p className="text-2xl font-bold text-purple-600">
                        {journalSummary.statistics.with_interpretation}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Last Updated</p>
                      <p className="text-sm font-medium text-gray-900">
                        {journalSummary.summary.last_updated 
                          ? new Date(journalSummary.summary.last_updated).toLocaleDateString()
                          : 'Unknown'
                        }
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Journal Types Overview */}
          {journalSummary && Object.keys(journalSummary.summary.journal_types).length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">Document Types</CardTitle>
                <CardDescription>Distribution of journal note types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(journalSummary.summary.journal_types).map(([type, count]) => (
                    <div key={type} className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-lg font-bold text-gray-900">{count}</div>
                      <div className="text-xs text-gray-600">{type.replace('_', ' ')}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Key Topics */}
          {journalSummary && journalSummary.summary.key_topics.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">Key Topics</CardTitle>
                <CardDescription>Common themes in journal notes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {journalSummary.summary.key_topics.map((topic, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Journal Notes Component */}
          <JournalNotes patientId={patientId} />
        </div>
      </main>
    </div>
  );
}

export default function JournalNotesPage() {
  return (
    <ProtectedRoute>
      <JournalNotesPageContent />
    </ProtectedRoute>
  );
}
