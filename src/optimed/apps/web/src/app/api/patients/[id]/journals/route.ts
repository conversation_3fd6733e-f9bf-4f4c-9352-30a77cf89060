import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const patientId = params.id;

    // Forward session headers for authentication
    const sessionId = request.headers.get('X-Session-ID');
    const headers: Record<string, string> = {};
    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Forward the request to the FastAPI backend
    const response = await fetch(`${BACKEND_URL}/patients/${patientId}/journals`, {
      method: 'GET',
      headers
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Patient journals fetch error:', error);
    return NextResponse.json(
      { detail: 'Failed to retrieve patient journals' },
      { status: 500 }
    );
  }
}
