import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const journalId = params.id;
    const url = new URL(request.url);
    const includeContent = url.searchParams.get('include_content') === 'true';
    const includeInterpretation = url.searchParams.get('include_interpretation') === 'true';

    // Forward session headers for authentication
    const sessionId = request.headers.get('X-Session-ID');
    const headers: Record<string, string> = {};
    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Build the backend URL with query parameters
    const backendUrl = new URL(`${BACKEND_URL}/journals/${journalId}`);
    if (includeContent) {
      backendUrl.searchParams.set('include_content', 'true');
    }
    if (includeInterpretation) {
      backendUrl.searchParams.set('include_interpretation', 'true');
    }

    // Forward the request to the FastAPI backend
    const response = await fetch(backendUrl.toString(), {
      method: 'GET',
      headers
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Journal fetch error:', error);
    return NextResponse.json(
      { detail: 'Journal not found' },
      { status: 404 }
    );
  }
}
