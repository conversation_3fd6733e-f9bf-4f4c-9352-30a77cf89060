import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const journalId = params.id;
    const url = new URL(request.url);
    const includeContent = url.searchParams.get('include_content') === 'true';
    const includeInterpretation = url.searchParams.get('include_interpretation') === 'true';

    // In a real implementation, this would call your FastAPI backend
    // For now, we'll return mock data
    const mockJournal = {
      journal_id: journalId,
      title: 'ACE Inhibitors in Elderly Hypertension: A Comprehensive Study',
      journal_type: 'RESEARCH_PAPER',
      patient_id: 'patient_123',
      processing_status: 'COMPLETED',
      uploaded_at: '2024-01-15T10:30:00Z',
      processed_at: '2024-01-15T10:30:45Z',
      metadata: {
        file_name: 'ace_inhibitors_elderly_study.pdf',
        file_size: 2048576,
        format: 'PDF',
        word_count: 2500,
        page_count: 12,
        author: 'Dr. <PERSON>, <PERSON>'
      }
    };

    // Add content if requested
    if (includeContent) {
      mockJournal.content = `This comprehensive study evaluates the efficacy and safety of ACE inhibitors in treating hypertension in elderly patients aged 65 and above.

**Background:**
Hypertension affects approximately 70% of adults over 65 years of age and is a major risk factor for cardiovascular disease, stroke, and kidney disease. The management of hypertension in elderly patients requires careful consideration of comorbidities, drug interactions, and age-related physiological changes.

**Methods:**
We conducted a randomized, double-blind, placebo-controlled trial involving 500 patients aged 65-85 years with essential hypertension. Participants were randomly assigned to receive either an ACE inhibitor (lisinopril 10-20mg daily) or placebo for 12 months.

**Results:**
The ACE inhibitor group showed a significant reduction in systolic blood pressure (mean reduction: 18.5 mmHg, 95% CI: 15.2-21.8, p<0.001) and diastolic blood pressure (mean reduction: 9.2 mmHg, 95% CI: 7.1-11.3, p<0.001) compared to placebo.

**Adverse Events:**
The incidence of adverse events was low and similar between groups. The most common side effect was a dry cough, occurring in 8% of patients in the ACE inhibitor group.

**Conclusion:**
ACE inhibitors are highly effective and well-tolerated for treating hypertension in elderly patients, with minimal side effects and significant cardiovascular benefits.`;

      mockJournal.abstract = 'This study demonstrates that ACE inhibitors are highly effective in treating hypertension in elderly patients with minimal side effects.';
    }

    // Add interpretation if requested
    if (includeInterpretation) {
      mockJournal.interpretation = {
        summary: 'This high-quality randomized controlled trial provides strong evidence for the efficacy and safety of ACE inhibitors in elderly hypertensive patients, showing significant blood pressure reduction with minimal adverse effects.',
        key_findings: [
          'Significant reduction in both systolic (18.5 mmHg) and diastolic (9.2 mmHg) blood pressure',
          'Low incidence of adverse events (8% dry cough)',
          'Well-tolerated in elderly population aged 65-85 years',
          'Suitable as first-line therapy for elderly hypertensive patients'
        ],
        clinical_significance: 'This study provides Level I evidence supporting ACE inhibitors as first-line therapy for hypertension in elderly patients, with implications for clinical guidelines and treatment protocols.',
        overall_confidence: 0.92,
        confidence_level: 'VERY_HIGH',
        diseases: ['hypertension', 'cardiovascular disease', 'essential hypertension'],
        medications: ['ACE inhibitors', 'lisinopril'],
        procedures: ['blood pressure monitoring', 'cardiovascular assessment'],
        symptoms: ['elevated blood pressure', 'dry cough']
      };
    }

    return NextResponse.json(mockJournal);
  } catch (error) {
    console.error('Journal fetch error:', error);
    return NextResponse.json(
      { detail: 'Journal not found' },
      { status: 404 }
    );
  }
}
