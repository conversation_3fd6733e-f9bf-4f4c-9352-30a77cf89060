import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const journalType = formData.get('journal_type') as string;
    const patientId = formData.get('patient_id') as string;

    if (!file) {
      return NextResponse.json(
        { detail: 'No file provided' },
        { status: 400 }
      );
    }

    // Forward the request to the FastAPI backend
    const backendFormData = new FormData();
    backendFormData.append('file', file);
    if (title) backendFormData.append('title', title);
    if (journalType) backendFormData.append('journal_type', journalType);
    if (patientId) backendFormData.append('patient_id', patientId);

    // Forward session headers for authentication
    const sessionId = request.headers.get('X-Session-ID');
    const headers: Record<string, string> = {};
    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    const response = await fetch(`${BACKEND_URL}/journals/upload`, {
      method: 'POST',
      body: backendFormData,
      headers
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { detail: 'Upload failed' },
      { status: 500 }
    );
  }
}
