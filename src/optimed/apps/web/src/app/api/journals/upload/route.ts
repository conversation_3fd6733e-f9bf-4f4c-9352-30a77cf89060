import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const title = formData.get('title') as string;
    const journalType = formData.get('journal_type') as string;
    const patientId = formData.get('patient_id') as string;

    if (!file) {
      return NextResponse.json(
        { detail: 'No file provided' },
        { status: 400 }
      );
    }

    // In a real implementation, this would call your FastAPI backend
    // For now, we'll return a mock response
    const mockResponse = {
      result_id: `result_${Date.now()}`,
      success: true,
      processing_time_seconds: Math.random() * 20 + 5, // 5-25 seconds
      journal: {
        journal_id: `journal_${Date.now()}`,
        title: title || file.name.replace(/\.[^/.]+$/, ''),
        journal_type: journalType || 'RESEARCH_PAPER',
        processing_status: 'COMPLETED',
        has_interpretation: true,
        word_count: Math.floor(Math.random() * 3000) + 500
      }
    };

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json(mockResponse);
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { detail: 'Upload failed' },
      { status: 500 }
    );
  }
}
