import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Forward session headers for authentication
    const sessionId = request.headers.get('X-Session-ID');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    // Forward the request to the FastAPI backend
    const response = await fetch(`${BACKEND_URL}/journals/search`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { detail: 'Search failed' },
      { status: 500 }
    );
  }
}
