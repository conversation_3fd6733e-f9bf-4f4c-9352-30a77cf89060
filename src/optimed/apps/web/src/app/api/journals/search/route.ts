import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, limit = 10 } = body;

    // In a real implementation, this would call your FastAPI backend
    // For now, we'll return mock data
    const mockJournals = [
      {
        journal_id: 'journal_001',
        title: 'ACE Inhibitors in Elderly Hypertension: A Comprehensive Study',
        journal_type: 'RESEARCH_PAPER',
        patient_id: 'patient_123',
        processing_status: 'COMPLETED',
        uploaded_at: '2024-01-15T10:30:00Z',
        word_count: 2500,
        has_interpretation: true
      },
      {
        journal_id: 'journal_002',
        title: 'Case Study: Resistant Hypertension Management in Elderly Patients',
        journal_type: 'CASE_STUDY',
        patient_id: 'patient_123',
        processing_status: 'COMPLETED',
        uploaded_at: '2024-01-10T14:20:00Z',
        word_count: 1200,
        has_interpretation: true
      },
      {
        journal_id: 'journal_003',
        title: 'Diabetes Management Guidelines 2024: Evidence-Based Recommendations',
        journal_type: 'CLINICAL_GUIDELINE',
        processing_status: 'COMPLETED',
        uploaded_at: '2024-01-08T09:15:00Z',
        word_count: 3200,
        has_interpretation: true
      },
      {
        journal_id: 'journal_004',
        title: 'Cardiovascular Risk Assessment in Type 2 Diabetes',
        journal_type: 'RESEARCH_PAPER',
        patient_id: 'patient_456',
        processing_status: 'COMPLETED',
        uploaded_at: '2024-01-05T16:45:00Z',
        word_count: 2800,
        has_interpretation: true
      },
      {
        journal_id: 'journal_005',
        title: 'Laboratory Reference Values for Elderly Populations',
        journal_type: 'LAB_REPORT',
        processing_status: 'COMPLETED',
        uploaded_at: '2024-01-03T11:30:00Z',
        word_count: 1500,
        has_interpretation: true
      }
    ];

    // Filter by query if provided
    let filteredJournals = mockJournals;
    if (query && query.trim()) {
      const searchTerm = query.toLowerCase();
      filteredJournals = mockJournals.filter(journal =>
        journal.title.toLowerCase().includes(searchTerm) ||
        journal.journal_type.toLowerCase().includes(searchTerm)
      );
    }

    // Apply limit
    const results = filteredJournals.slice(0, limit);

    return NextResponse.json(results);
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { detail: 'Search failed' },
      { status: 500 }
    );
  }
}
