'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  FileText, 
  Calendar, 
  Search, 
  ChevronRight, 
  ChevronDown, 
  Clock,
  Database,
  User,
  Filter,
  X
} from "lucide-react";

interface JournalNote {
  journal_id: string;
  title: string;
  type: string;
  source: string;
  uploaded_at: string | null;
  word_count: number;
  has_interpretation: boolean;
  content_preview: string | null;
}

interface JournalNotesProps {
  patientId: string;
  className?: string;
}

export function JournalNotes({ patientId, className = "" }: JournalNotesProps) {
  const [journalNotes, setJournalNotes] = useState<JournalNote[]>([]);
  const [selectedNote, setSelectedNote] = useState<JournalNote | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [sourceFilter, setSourceFilter] = useState<string>('');

  // Fetch journal notes from the backend
  const fetchJournalNotes = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/patients/${patientId}/journal-notes?limit=20`, {
        headers: {
          'X-Session-ID': localStorage.getItem('session_id') || ''
        }
      });

      if (response.ok) {
        const data = await response.json();
        setJournalNotes(data);
      } else {
        console.error('Failed to fetch journal notes:', response.statusText);
        // Mock data for demonstration
        setJournalNotes([
          {
            journal_id: 'note_001',
            title: 'Discharge Summary - Hypertension Management',
            type: 'DISCHARGE_SUMMARY',
            source: 'EHR',
            uploaded_at: '2024-01-15T10:30:00Z',
            word_count: 450,
            has_interpretation: true,
            content_preview: 'Patient discharged with improved blood pressure control. Continue ACE inhibitor therapy and follow up in 2 weeks...'
          },
          {
            journal_id: 'note_002',
            title: 'Progress Note - Daily Assessment',
            type: 'PROGRESS_NOTE',
            source: 'EHR',
            uploaded_at: '2024-01-14T14:20:00Z',
            word_count: 280,
            has_interpretation: true,
            content_preview: 'Patient shows good response to current treatment regimen. Blood pressure stable at 130/80...'
          },
          {
            journal_id: 'note_003',
            title: 'Laboratory Report - Lipid Panel',
            type: 'LAB_REPORT',
            source: 'EHR',
            uploaded_at: '2024-01-12T09:15:00Z',
            word_count: 150,
            has_interpretation: false,
            content_preview: 'Total cholesterol: 185 mg/dL, LDL: 110 mg/dL, HDL: 45 mg/dL, Triglycerides: 150 mg/dL...'
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching journal notes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJournalNotes();
  }, [patientId]);

  // Filter notes based on search and filters
  const filteredNotes = journalNotes.filter(note => {
    const matchesSearch = !searchQuery || 
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (note.content_preview && note.content_preview.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesType = !typeFilter || note.type === typeFilter;
    const matchesSource = !sourceFilter || note.source === sourceFilter;
    
    return matchesSearch && matchesType && matchesSource;
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'DISCHARGE_SUMMARY': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'PROGRESS_NOTE': return 'bg-green-100 text-green-800 border-green-200';
      case 'LAB_REPORT': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CONSULTATION_NOTE': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'PROCEDURE_NOTE': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSourceIcon = (source: string) => {
    return source === 'EHR' ? <Database className="h-3 w-3" /> : <User className="h-3 w-3" />;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Unknown date';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const clearFilters = () => {
    setSearchQuery('');
    setTypeFilter('');
    setSourceFilter('');
  };

  const uniqueTypes = [...new Set(journalNotes.map(note => note.type))];
  const uniqueSources = [...new Set(journalNotes.map(note => note.source))];

  return (
    <div className={`flex flex-col lg:flex-row gap-4 h-[600px] ${className}`}>
      {/* Left Panel - Journal Notes List */}
      <div className="w-full lg:w-1/2 flex flex-col">
        <Card className="flex-1 flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Journal Notes
                </CardTitle>
                <CardDescription>
                  Clinical notes and documentation from EHR
                </CardDescription>
              </div>
              <Badge variant="outline" className="text-sm">
                {filteredNotes.length} notes
              </Badge>
            </div>
            
            {/* Search and Filters */}
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search notes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-1 border rounded-md text-sm"
                >
                  <option value="">All Types</option>
                  {uniqueTypes.map(type => (
                    <option key={type} value={type}>
                      {type.replace('_', ' ')}
                    </option>
                  ))}
                </select>
                
                <select
                  value={sourceFilter}
                  onChange={(e) => setSourceFilter(e.target.value)}
                  className="px-3 py-1 border rounded-md text-sm"
                >
                  <option value="">All Sources</option>
                  {uniqueSources.map(source => (
                    <option key={source} value={source}>{source}</option>
                  ))}
                </select>
                
                {(searchQuery || typeFilter || sourceFilter) && (
                  <Button
                    onClick={clearFilters}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <X className="h-3 w-3" />
                    Clear
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="flex-1 overflow-hidden p-0">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Loading notes...</p>
                </div>
              </div>
            ) : (
              <div className="overflow-y-auto h-full">
                {filteredNotes.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-center p-6">
                    <div>
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No notes found</h3>
                      <p className="text-gray-600">
                        {journalNotes.length === 0 
                          ? "No journal notes available for this patient."
                          : "No notes match your current filters."
                        }
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2 p-4">
                    {filteredNotes.map((note) => (
                      <div
                        key={note.journal_id}
                        onClick={() => setSelectedNote(note)}
                        className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                          selectedNote?.journal_id === note.journal_id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-gray-900 text-sm leading-tight">
                            {note.title}
                          </h4>
                          <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0 ml-2" />
                        </div>
                        
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={`text-xs ${getTypeColor(note.type)}`}>
                            {note.type.replace('_', ' ')}
                          </Badge>
                          <Badge variant="outline" className="text-xs flex items-center gap-1">
                            {getSourceIcon(note.source)}
                            {note.source}
                          </Badge>
                          {note.has_interpretation && (
                            <Badge variant="outline" className="text-xs text-green-600 border-green-600">
                              AI Analyzed
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-600 mb-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(note.uploaded_at)}
                          </div>
                          <div>{note.word_count} words</div>
                        </div>
                        
                        {note.content_preview && (
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {note.content_preview}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Right Panel - Selected Note Detail */}
      <div className="w-full lg:w-1/2">
        <Card className="h-full">
          {selectedNote ? (
            <>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg leading-tight mb-2">
                      {selectedNote.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className={getTypeColor(selectedNote.type)}>
                        {selectedNote.type.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline" className="flex items-center gap-1">
                        {getSourceIcon(selectedNote.source)}
                        {selectedNote.source}
                      </Badge>
                      {selectedNote.has_interpretation && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          AI Analyzed
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {formatDate(selectedNote.uploaded_at)}
                      </div>
                      <div>{selectedNote.word_count} words</div>
                    </div>
                  </div>
                  <Button
                    onClick={() => setSelectedNote(null)}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <X className="h-4 w-4" />
                    Close
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="flex-1 overflow-y-auto">
                <div className="prose prose-sm max-w-none">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Content</h4>
                  <div className="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                    {selectedNote.content_preview || "Full content would be loaded here..."}
                  </div>
                  
                  {selectedNote.has_interpretation && (
                    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h5 className="text-sm font-medium text-green-900 mb-2">AI Analysis</h5>
                      <p className="text-sm text-green-800">
                        This document has been analyzed by AI for clinical insights and key findings.
                        <Button variant="link" className="p-0 h-auto text-green-700 underline ml-1">
                          View analysis
                        </Button>
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </>
          ) : (
            <CardContent className="flex items-center justify-center h-full text-center">
              <div>
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a note</h3>
                <p className="text-gray-600">
                  Click on a journal note from the list to view its details.
                </p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
