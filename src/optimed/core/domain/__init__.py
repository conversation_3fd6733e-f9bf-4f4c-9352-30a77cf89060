from .clinical import PatientContext, BedState, Encounter
from .diagnosis import <PERSON>ag<PERSON><PERSON><PERSON>ult
from .info import InfoEvent, Notification, CommThread, ChatMessage
from .journal import (
    JournalEntry, JournalInterpretation, DocumentMetadata, JournalContext,
    JournalSearchQuery, JournalProcessingResult, JournalUploadResult,
    JournalDomainError, InvalidJournalError, JournalNotFoundError, JournalProcessingError
)
from .kpi import KPIEvent, Alert
from .users import User, UserAction, UserSession
from .enums import (
    BedStatus,
    EncounterStatus,
    EventType,
    Channel,
    NotificationStatus,
    Severity,
    ChatRole,
    UserRole,
    UserStatus,
    JournalType,
    ProcessingStatus,
    DocumentFormat,
)

"""OptiMed domain entities (v0).

All classes are **immutable** (Pydantic frozen models) and contain only
*side‑effect‑free* helper methods.  External I/O lives in adapters or
service layers.
"""

__all__ = [
    # clinical
    "PatientContext",
    "Encounter",
    "BedState",
    # info
    "InfoEvent",
    "Notification",
    "CommThread",
    "ChatMessage",
    # journal
    "JournalEntry",
    "JournalInterpretation",
    "DocumentMetadata",
    "JournalContext",
    "JournalSearchQuery",
    "JournalProcessingResult",
    "JournalUploadResult",
    "JournalDomainError",
    "InvalidJournalError",
    "JournalNotFoundError",
    "JournalProcessingError",
    # kpi
    "KPIEvent",
    "Alert",
    # enums
    "BedStatus",
    "EncounterStatus",
    "EventType",
    "Channel",
    "NotificationStatus",
    "Severity",
    "ChatRole",
    "JournalType",
    "ProcessingStatus",
    "DocumentFormat",
    # diagnosis
    "DiagnosisResult",
    # users
    "User",
    "UserAction",
    "UserSession",
    "UserRole",
    "UserStatus",
]