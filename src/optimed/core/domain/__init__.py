from .clinical import PatientContext, BedState, Encounter
from .diagnosis import Diagnosis<PERSON><PERSON>ult
from .info import InfoEvent, Notification, CommThread, ChatMessage
from .kpi import KPIEvent, Alert
from .users import User, UserAction, UserSession
from .journal import (
    DocumentMetadata,
    JournalEntry,
    MedicalInsight,
    JournalInterpretation,
    ProcessingResult
)
from .enums import (
    BedStatus,
    EncounterStatus,
    EventType,
    Channel,
    NotificationStatus,
    Severity,
    ChatRole,
    UserRole,
    UserStatus,
    DocumentFormat,
    JournalType,
    ProcessingStatus,
    InterpretationConfidence,
)

"""OptiMed domain entities (v0).

All classes are **immutable** (Pydantic frozen models) and contain only
*side‑effect‑free* helper methods.  External I/O lives in adapters or
service layers.
"""

__all__ = [
    # clinical
    "PatientContext",
    "Encounter",
    "BedState",
    # info
    "InfoEvent",
    "Notification",
    "CommThread",
    "ChatMessage",
    # kpi
    "KPIEvent",
    "Alert",
    # journal
    "DocumentMetadata",
    "JournalEntry",
    "MedicalInsight",
    "JournalInterpretation",
    "ProcessingResult",
    # enums
    "BedStatus",
    "EncounterStatus",
    "EventType",
    "Channel",
    "NotificationStatus",
    "Severity",
    "ChatRole",
    "DocumentFormat",
    "JournalType",
    "ProcessingStatus",
    "InterpretationConfidence",
    # diagnosis
    "DiagnosisResult",
    # users
    "User",
    "UserAction",
    "UserSession",
    "UserRole",
    "UserStatus",
]