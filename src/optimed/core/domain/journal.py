"""
Journal domain objects for medical journal and clinical notes management.

This module defines the core domain objects for handling medical journals,
clinical notes, and related metadata following hexagonal architecture principles.
"""

from __future__ import annotations
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from enum import Enum

from .enums import JournalType, ProcessingStatus, DocumentFormat


@dataclass
class DocumentMetadata:
    """Metadata for journal documents."""
    file_name: str
    file_size: int
    format: DocumentFormat
    mime_type: Optional[str] = None
    word_count: Optional[int] = None
    page_count: Optional[int] = None
    author: Optional[str] = None
    created_at: Optional[datetime] = None
    title: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at and self.created_at.tzinfo is None:
            self.created_at = self.created_at.replace(tzinfo=timezone.utc)


@dataclass
class JournalInterpretation:
    """AI interpretation of journal content."""
    summary: str
    key_findings: List[str]
    clinical_significance: str
    overall_confidence: float
    confidence_level: str
    diseases: List[str] = field(default_factory=list)
    medications: List[str] = field(default_factory=list)
    procedures: List[str] = field(default_factory=list)
    symptoms: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        # Ensure confidence is between 0 and 1
        if not 0 <= self.overall_confidence <= 1:
            raise ValueError("Confidence must be between 0 and 1")


@dataclass
class JournalEntry:
    """Core domain object representing a medical journal entry or clinical note."""
    journal_id: str
    title: str
    content: str
    journal_type: JournalType
    metadata: DocumentMetadata
    processing_status: ProcessingStatus
    patient_id: Optional[str] = None
    user_id: Optional[str] = None
    uploaded_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None
    abstract: Optional[str] = None
    interpretation: Optional[JournalInterpretation] = None
    tags: List[str] = field(default_factory=list)
    source: str = "UPLOAD"  # "UPLOAD" or "EHR"
    
    def __post_init__(self):
        # Set default timestamps
        if self.uploaded_at is None:
            self.uploaded_at = datetime.now(timezone.utc)
        elif self.uploaded_at.tzinfo is None:
            self.uploaded_at = self.uploaded_at.replace(tzinfo=timezone.utc)
            
        if self.processed_at and self.processed_at.tzinfo is None:
            self.processed_at = self.processed_at.replace(tzinfo=timezone.utc)
    
    @property
    def has_interpretation(self) -> bool:
        """Check if journal has AI interpretation."""
        return self.interpretation is not None
    
    @property
    def is_from_ehr(self) -> bool:
        """Check if journal was sourced from EHR."""
        return self.source == "EHR" or self.journal_id.startswith("fhir_")
    
    @property
    def word_count(self) -> int:
        """Get word count from metadata or calculate from content."""
        if self.metadata.word_count:
            return self.metadata.word_count
        return len(self.content.split()) if self.content else 0
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the journal entry."""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the journal entry."""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def update_processing_status(self, status: ProcessingStatus, processed_at: Optional[datetime] = None) -> None:
        """Update the processing status of the journal."""
        self.processing_status = status
        if status == ProcessingStatus.COMPLETED and processed_at:
            self.processed_at = processed_at
        elif status == ProcessingStatus.COMPLETED and not self.processed_at:
            self.processed_at = datetime.now(timezone.utc)


@dataclass
class JournalSearchQuery:
    """Search query for journal entries."""
    query: Optional[str] = None
    patient_id: Optional[str] = None
    journal_type: Optional[JournalType] = None
    processing_status: Optional[ProcessingStatus] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)
    has_interpretation: Optional[bool] = None
    source: Optional[str] = None
    limit: int = 10
    offset: int = 0
    
    def __post_init__(self):
        if self.date_from and self.date_from.tzinfo is None:
            self.date_from = self.date_from.replace(tzinfo=timezone.utc)
        if self.date_to and self.date_to.tzinfo is None:
            self.date_to = self.date_to.replace(tzinfo=timezone.utc)


@dataclass
class JournalContext:
    """Context information about journals for a patient."""
    patient_id: str
    total_journals: int
    journal_types: Dict[str, int]
    recent_journals: List[Dict[str, Any]]
    key_topics: List[str]
    relevant_journals: List[Dict[str, Any]] = field(default_factory=list)
    query_used: Optional[str] = None
    last_updated: Optional[datetime] = None
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.now(timezone.utc)
        elif self.last_updated.tzinfo is None:
            self.last_updated = self.last_updated.replace(tzinfo=timezone.utc)


@dataclass
class JournalProcessingResult:
    """Result of journal processing operation."""
    journal_id: str
    success: bool
    processing_time_seconds: float
    interpretation: Optional[JournalInterpretation] = None
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    
    @property
    def has_warnings(self) -> bool:
        """Check if processing had warnings."""
        return len(self.warnings) > 0


@dataclass
class JournalUploadResult:
    """Result of journal upload operation."""
    result_id: str
    success: bool
    processing_time_seconds: float
    journal: JournalEntry
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "result_id": self.result_id,
            "success": self.success,
            "processing_time_seconds": self.processing_time_seconds,
            "journal": {
                "journal_id": self.journal.journal_id,
                "title": self.journal.title,
                "journal_type": self.journal.journal_type.value,
                "processing_status": self.journal.processing_status.value,
                "has_interpretation": self.journal.has_interpretation,
                "word_count": self.journal.word_count
            },
            "error_message": self.error_message
        }


# Domain exceptions
class JournalDomainError(Exception):
    """Base exception for journal domain errors."""
    pass


class InvalidJournalError(JournalDomainError):
    """Raised when journal data is invalid."""
    pass


class JournalNotFoundError(JournalDomainError):
    """Raised when journal is not found."""
    pass


class JournalProcessingError(JournalDomainError):
    """Raised when journal processing fails."""
    pass
