from __future__ import annotations

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path

from pydantic import Field, validator

from .enums import DocumentFormat, JournalType, ProcessingStatus, InterpretationConfidence
from .mixins import FrozenModel

"""Domain models for medical journal and document processing."""


class DocumentMetadata(FrozenModel):
    """Metadata about a journal document."""
    
    file_name: str
    file_size: int  # bytes
    format: DocumentFormat
    mime_type: Optional[str] = None
    encoding: Optional[str] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    language: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    author: Optional[str] = None
    title: Optional[str] = None
    subject: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    
    @validator('file_size')
    def validate_file_size(cls, v):
        if v < 0:
            raise ValueError("File size must be non-negative")
        return v


class JournalEntry(FrozenModel):
    """Core domain model for a medical journal document."""
    
    journal_id: str
    patient_id: Optional[str] = None  # Link to patient if applicable
    user_id: Optional[str] = None     # User who uploaded/created
    session_id: Optional[str] = None  # Session context
    
    # Document properties
    title: str
    content: str  # Raw extracted text content
    journal_type: JournalType
    metadata: DocumentMetadata
    
    # Processing state
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    uploaded_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    processed_at: Optional[datetime] = None
    
    # Source information
    source_url: Optional[str] = None
    doi: Optional[str] = None
    pmid: Optional[str] = None  # PubMed ID
    journal_name: Optional[str] = None
    publication_date: Optional[datetime] = None
    authors: List[str] = Field(default_factory=list)
    
    # Content structure
    abstract: Optional[str] = None
    sections: Dict[str, str] = Field(default_factory=dict)  # section_name -> content
    references: List[str] = Field(default_factory=list)
    figures: List[str] = Field(default_factory=list)
    tables: List[str] = Field(default_factory=list)
    
    def get_section(self, section_name: str) -> Optional[str]:
        """Get content of a specific section."""
        return self.sections.get(section_name.lower())
    
    def has_patient_context(self) -> bool:
        """Check if this journal entry is linked to a specific patient."""
        return self.patient_id is not None
    
    def get_word_count(self) -> int:
        """Get approximate word count of the content."""
        return len(self.content.split()) if self.content else 0


class MedicalInsight(FrozenModel):
    """A specific medical insight extracted from journal content."""
    
    insight_id: str
    category: str  # e.g., "diagnosis", "treatment", "prognosis", "risk_factor"
    content: str
    confidence: float  # 0.0 to 1.0
    confidence_level: InterpretationConfidence
    source_section: Optional[str] = None  # Which section this came from
    evidence_text: Optional[str] = None   # Supporting text from document
    icd_codes: List[str] = Field(default_factory=list)
    medical_terms: List[str] = Field(default_factory=list)
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Confidence must be between 0.0 and 1.0")
        return v
    
    @validator('confidence_level', pre=True, always=True)
    def set_confidence_level(cls, v, values):
        if 'confidence' in values:
            conf = values['confidence']
            if conf >= 0.9:
                return InterpretationConfidence.VERY_HIGH
            elif conf >= 0.7:
                return InterpretationConfidence.HIGH
            elif conf >= 0.5:
                return InterpretationConfidence.MEDIUM
            elif conf >= 0.3:
                return InterpretationConfidence.LOW
            else:
                return InterpretationConfidence.VERY_LOW
        return v


class JournalInterpretation(FrozenModel):
    """AI-generated interpretation and insights from a journal document."""
    
    interpretation_id: str
    journal_id: str
    
    # Summary and key findings
    summary: str
    key_findings: List[str] = Field(default_factory=list)
    medical_insights: List[MedicalInsight] = Field(default_factory=list)
    
    # Clinical relevance
    clinical_significance: str
    patient_applicability: Optional[str] = None  # If linked to specific patient
    treatment_implications: List[str] = Field(default_factory=list)
    diagnostic_implications: List[str] = Field(default_factory=list)
    
    # Quality and reliability
    overall_confidence: float  # 0.0 to 1.0
    confidence_level: InterpretationConfidence
    quality_score: float  # 0.0 to 1.0 - quality of source document
    evidence_level: Optional[str] = None  # e.g., "Level I", "Level II", etc.
    
    # Processing metadata
    interpreted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    interpreted_by_model: str  # AI model used for interpretation
    processing_time_seconds: Optional[float] = None
    
    # Extracted entities
    diseases: List[str] = Field(default_factory=list)
    medications: List[str] = Field(default_factory=list)
    procedures: List[str] = Field(default_factory=list)
    symptoms: List[str] = Field(default_factory=list)
    risk_factors: List[str] = Field(default_factory=list)
    
    @validator('overall_confidence', 'quality_score')
    def validate_scores(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Score must be between 0.0 and 1.0")
        return v
    
    def get_high_confidence_insights(self, min_confidence: float = 0.7) -> List[MedicalInsight]:
        """Get insights above a confidence threshold."""
        return [insight for insight in self.medical_insights 
                if insight.confidence >= min_confidence]
    
    def get_insights_by_category(self, category: str) -> List[MedicalInsight]:
        """Get insights of a specific category."""
        return [insight for insight in self.medical_insights 
                if insight.category.lower() == category.lower()]


class ProcessingResult(FrozenModel):
    """Complete result of journal processing pipeline."""
    
    result_id: str
    journal_entry: JournalEntry
    interpretation: Optional[JournalInterpretation] = None
    
    # Processing metadata
    processing_started_at: datetime
    processing_completed_at: Optional[datetime] = None
    total_processing_time_seconds: Optional[float] = None
    
    # Success/failure information
    success: bool = True
    error_message: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    
    # Vector embedding information
    embedding_id: Optional[str] = None
    embedding_stored: bool = False
    
    def is_complete(self) -> bool:
        """Check if processing completed successfully."""
        return (self.success and 
                self.processing_completed_at is not None and
                self.interpretation is not None)
    
    def get_processing_duration(self) -> Optional[float]:
        """Get total processing time in seconds."""
        if self.processing_completed_at and self.processing_started_at:
            return (self.processing_completed_at - self.processing_started_at).total_seconds()
        return self.total_processing_time_seconds
