from enum import Enum

"""Enum definitions shared across the domain layer."""

__all__ = [
    "BedStatus",
    "EncounterStatus",
    "EventType",
    "Channel",
    "NotificationStatus",
    "Severity",
    "DocumentFormat",
    "JournalType",
    "ProcessingStatus",
    "InterpretationConfidence",
]


class BedStatus(str, Enum):
    VACANT = "VACANT"
    OCCUPIED = "OCCUPIED"
    CLEANING = "CLEANING"

class EncounterStatus(str, Enum):
    IN_PROGRESS = "IN_PROGRESS"
    DISCHARGED = "DISCHARGED"

class EventType(str, Enum):
    ADMISSION = "ADMISSION"
    DISCHARGE = "DISCHARGE"
    TRANSFER = "TRANSFER"
    # Vitals
    VITAL_READING = "VITAL_READING"
    VITAL_ALERT = "VITAL_ALERT"

    # Labs
    LAB_RESULT = "LAB_RESULT"
    LAB_CRIT = "LAB_CRIT"

    # Imaging
    RAD_READY = "RAD_READY"
    RAD_CRIT = "RAD_CRIT"

    # Medication & orders
    MED_ORDER = "MED_ORDER"
    MED_INTERACTION = "MED_INTERACTION"

    # Patient movement / logistics
    PATIENT_MOVE = "PATIENT_MOVE"
    BED_STATE_CHANGE = "BED_STATE_CHANGE"

    # System capacity
    SYS_BACKLOG = "SYS_BACKLOG"
    SYS_CRIT_BACKLOG = "SYS_CRIT_BACKLOG"

    # Documentation / NLP watch
    NOTE_NEW = "NOTE_NEW"
    NOTE_SEPSIS_KEYWORD = "NOTE_SEPSIS_KEYWORD"

class Channel(str, Enum):
    TEAMS = "TEAMS"
    EMAIL = "EMAIL"
    ASCOM = "ASCOM"
    SMS = "SMS"
    IN_APP = "IN_APP"


class DocumentFormat(str, Enum):
    """Supported document formats for journal processing."""
    PDF = "PDF"
    DOCX = "DOCX"
    DOC = "DOC"
    TXT = "TXT"
    RTF = "RTF"
    JSON = "JSON"
    XML = "XML"
    HTML = "HTML"
    MARKDOWN = "MARKDOWN"
    UNKNOWN = "UNKNOWN"


class JournalType(str, Enum):
    """Types of medical journals and documents."""
    RESEARCH_PAPER = "RESEARCH_PAPER"
    CASE_STUDY = "CASE_STUDY"
    CLINICAL_TRIAL = "CLINICAL_TRIAL"
    REVIEW_ARTICLE = "REVIEW_ARTICLE"
    EDITORIAL = "EDITORIAL"
    LETTER = "LETTER"
    CONFERENCE_ABSTRACT = "CONFERENCE_ABSTRACT"
    CLINICAL_GUIDELINE = "CLINICAL_GUIDELINE"
    PATIENT_RECORD = "PATIENT_RECORD"
    LAB_REPORT = "LAB_REPORT"
    IMAGING_REPORT = "IMAGING_REPORT"
    DISCHARGE_SUMMARY = "DISCHARGE_SUMMARY"
    PROGRESS_NOTE = "PROGRESS_NOTE"
    UNKNOWN = "UNKNOWN"


class ProcessingStatus(str, Enum):
    """Status of journal document processing."""
    PENDING = "PENDING"
    PARSING = "PARSING"
    INTERPRETING = "INTERPRETING"
    EMBEDDING = "EMBEDDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class InterpretationConfidence(str, Enum):
    """Confidence levels for AI interpretation results."""
    VERY_HIGH = "VERY_HIGH"  # 0.9-1.0
    HIGH = "HIGH"            # 0.7-0.89
    MEDIUM = "MEDIUM"        # 0.5-0.69
    LOW = "LOW"              # 0.3-0.49
    VERY_LOW = "VERY_LOW"    # 0.0-0.29

class NotificationStatus(str, Enum):
    DELIVERED = "DELIVERED"
    ACK = "ACK"
    TIMEOUT = "TIMEOUT"

class Severity(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ChatRole(str, Enum):
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


class UserRole(str, Enum):
    ADMIN = "admin"
    DOCTOR = "doctor"
    NURSE = "nurse"
    TECHNICIAN = "technician"
    OTHER_STAFF = "other_staff"

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

