from __future__ import annotations

from typing import AsyncGenerator, Protocol, Sequence, Any, Union, Optional

from optimed.core.domain import (
    PatientContext, KPIEvent, ChatMessage, DiagnosisResult, User, UserSession, UserAction,
    JournalEntry, DocumentMetadata, JournalInterpretation, ProcessingResult, MedicalInsight,
    DocumentFormat, JournalType
)

# core/ports.py
#
# Hexagonal “ports” that your adapters (Issue #8 Claude, #9 FHIR, #10 pgvector)
# will implement.  Nothing here should import concrete infrastructure libs.


class FHIRRepository(Protocol):
    """Read-only access to EHR data expoised as FHIR / SMART-onFHIR APIs."""

    async def get_patient(self, patient_id: str) -> PatientContext:
        """Get patient context by ID."""
        ...

    async def search_patients(self, query: str) -> Sequence[PatientContext]:
        """Simple convenience search"""
        ...

    async def close(self) -> None:
        """Close any resources like HTTP clients."""
        ...

    async def _get_patient_resource(self, pid: str) -> PatientContext:
        """Fetch a single Patient resource by ID."""
        ...
    
    async def _get_observations(
        self, pid: str, *, category: str, count: int = 10
    ) -> list[Any]:
        """Fetch observations of a specific category for a patient."""
        ...



class KPIEventSink(Protocol):

    async def record(self, event: KPIEvent) -> None:
        """Record a KPI event."""
        ...
    
    async def flush(self) -> None:
        """Flush any buffered events."""
        ...


class LLMClient(Protocol):

    async def chat(
        self,
        messages: Sequence[ChatMessage],
        json_mode: bool = False,
        temperature: float = 0.7,
        max_tokens: int | None = None,
    ) -> ChatMessage:
        """Send a chat message sequence to the LLM and get a response."""
        ...

class VectorStore(Protocol):

    """pgvector / Pinecone / FAISS – whatever backs similarity search."""

    async def upsert(
        self,
        embedding_id: str,
        embedding: Sequence[float],
        object_type: str,
        object_id: str,
        content: str | dict,
        metadata: dict[str, Any] | None = None,
    ) -> str:
        """Upsert an embedding vector with optional metadata."""
        ...

    async def similarity_search(
        self,
        embedding: Sequence[float],
        top_k: int = 5,
        filter_: dict[str, str] | None = None, 
    ) -> Sequence[tuple[str, float, str]]:
        """Find top-k most similar embeddings."""
        ...

    async def delete(self, embedding_ids: Sequence[str]) -> None:
        """Delete an embedding vector by ID."""
        ...

    async def get_chat_history(self, patient_id: str, session_id: str | None = None, limit: int = 20) -> list[dict[str, Any]]:
        """Get chat history for a patient."""
        ...

    async def clear_chat_history(self, patient_id: str) -> None:
        """Clear chat history for a patient."""
        ...

    async def update(
        self,
        embedding_id: str,
        embedding: Sequence[float] | None = None,
        metadata: dict[str, Any] | None = None,
    ) -> None:
        """Update an existing embedding vector's metadata or embedding."""
        ...


class DiagnosticEngine(Protocol):
    """Pure-domain service used by LangGraph; orchestrates everything."""

    async def run(self, patient: PatientContext | None = None, user_prompt: str | None = None, chat_mode: bool = False, user_id: str | None = None, session_id: str | None = None) -> Union[DiagnosisResult,  dict]:
        """Return the top diagnosis (v0) or ranked list (v1)."""
        ...

    def run_stream(self, patient: PatientContext, user_prompt: str | None = None) -> AsyncGenerator[dict[str, Any], None]:
        """Run the diagnostic engine with streaming support."""
        ...

    @property
    def vector_store(self) -> VectorStore:
        """Access to the underlying vector store."""
        ...

class UserRepository(Protocol):
    """User management and session handling."""

    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        ...

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        ...

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        ...

    async def create_user(self, user: User) -> User:
        """Create a new user."""
        ...
    
    async def update_user(self, user: User) -> User:
        """Update an existing user."""
        ... 

    async def delete_user(self, user_id: str) -> None:
        """Delete a user by ID."""
        ...
    
    async def list_users_by_role(self, role: str) -> Sequence[User]:
        """List users by role."""
        ...

    async def create_session(self, session: UserSession) -> UserSession:
        """Create a new user session."""
        ...

    async def update_session_activity(self, session_id: str) -> None:
        """Update the last activity timestamp of a user session."""
        ...

    async def get_active_session(self, session_id: str) -> Optional[UserSession]:
        """Get an active user session by ID."""
        ...
    
    async def log_action(self, action: UserAction) -> UserAction:
        """Log a user action."""
        ...


# Journal Processing Ports
# ========================

class DocumentParser(Protocol):
    """Parse documents of various formats into structured text."""

    async def parse(self, file_path: str | bytes, format_hint: DocumentFormat | None = None) -> tuple[str, DocumentMetadata]:
        """Parse a document and return content and metadata.

        Args:
            file_path: Path to file or raw bytes
            format_hint: Optional format hint to optimize parsing

        Returns:
            Tuple of (extracted_text, metadata)
        """
        ...

    def supports_format(self, format: DocumentFormat) -> bool:
        """Check if this parser supports the given format."""
        ...


class FormatDetector(Protocol):
    """Detect document format and structure."""

    async def detect_format(self, file_path: str | bytes, filename: str | None = None) -> DocumentFormat:
        """Detect the format of a document.

        Args:
            file_path: Path to file or raw bytes
            filename: Optional filename for extension-based detection

        Returns:
            Detected document format
        """
        ...

    async def detect_journal_type(self, content: str, metadata: DocumentMetadata) -> JournalType:
        """Detect the type of medical journal/document.

        Args:
            content: Extracted text content
            metadata: Document metadata

        Returns:
            Detected journal type
        """
        ...


class JournalInterpreter(Protocol):
    """AI-powered interpretation of medical journal content."""

    async def interpret(self, journal_entry: JournalEntry) -> JournalInterpretation:
        """Generate AI interpretation of journal content.

        Args:
            journal_entry: The journal entry to interpret

        Returns:
            AI-generated interpretation and insights
        """
        ...

    async def extract_insights(self, content: str, journal_type: JournalType) -> Sequence[MedicalInsight]:
        """Extract specific medical insights from content.

        Args:
            content: Text content to analyze
            journal_type: Type of journal for context

        Returns:
            List of extracted medical insights
        """
        ...

    async def summarize(self, content: str, max_length: int = 500) -> str:
        """Generate a summary of the journal content.

        Args:
            content: Text content to summarize
            max_length: Maximum length of summary

        Returns:
            Generated summary
        """
        ...


class JournalRepository(Protocol):
    """Storage and retrieval of journal data."""

    async def store_journal(self, journal_entry: JournalEntry) -> str:
        """Store a journal entry.

        Args:
            journal_entry: The journal entry to store

        Returns:
            Stored journal ID
        """
        ...

    async def get_journal(self, journal_id: str) -> Optional[JournalEntry]:
        """Retrieve a journal entry by ID.

        Args:
            journal_id: ID of the journal to retrieve

        Returns:
            Journal entry if found, None otherwise
        """
        ...

    async def store_interpretation(self, interpretation: JournalInterpretation) -> str:
        """Store a journal interpretation.

        Args:
            interpretation: The interpretation to store

        Returns:
            Stored interpretation ID
        """
        ...

    async def get_interpretation(self, journal_id: str) -> Optional[JournalInterpretation]:
        """Get interpretation for a journal.

        Args:
            journal_id: ID of the journal

        Returns:
            Interpretation if found, None otherwise
        """
        ...

    async def search_journals(
        self,
        query: str,
        journal_type: JournalType | None = None,
        patient_id: str | None = None,
        limit: int = 10
    ) -> Sequence[JournalEntry]:
        """Search journals by content or metadata.

        Args:
            query: Search query
            journal_type: Optional filter by journal type
            patient_id: Optional filter by patient
            limit: Maximum number of results

        Returns:
            List of matching journal entries
        """
        ...

    async def get_patient_journals(self, patient_id: str) -> Sequence[JournalEntry]:
        """Get all journals associated with a patient.

        Args:
            patient_id: Patient ID

        Returns:
            List of journal entries for the patient
        """
        ...


class JournalProcessor(Protocol):
    """End-to-end journal processing orchestrator."""

    async def process_document(
        self,
        file_path: str | bytes,
        filename: str | None = None,
        patient_id: str | None = None,
        user_id: str | None = None,
        session_id: str | None = None
    ) -> ProcessingResult:
        """Process a document end-to-end.

        Args:
            file_path: Path to file or raw bytes
            filename: Optional filename
            patient_id: Optional patient association
            user_id: Optional user who uploaded
            session_id: Optional session context

        Returns:
            Complete processing result
        """
        ...

    async def reprocess_journal(self, journal_id: str) -> ProcessingResult:
        """Reprocess an existing journal with updated AI models.

        Args:
            journal_id: ID of journal to reprocess

        Returns:
            Updated processing result
        """
        ...