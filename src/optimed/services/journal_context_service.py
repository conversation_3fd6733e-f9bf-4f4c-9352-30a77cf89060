"""Service for retrieving and formatting journal context for diagnostic reasoning."""

from __future__ import annotations

import json
from typing import List, Dict, Any, Optional, Sequence

from optimed.core.ports import VectorStore, JournalRepository
from optimed.core.domain import PatientContext, JournalEntry, JournalInterpretation
from optimed.embeddings.minilm import embed


class JournalContextService:
    """Service for retrieving relevant journal context for patient diagnostics."""

    def __init__(self, vector_store: VectorStore, journal_repository: Optional[JournalRepository] = None):
        self.vector_store = vector_store
        self.journal_repository = journal_repository

    async def get_relevant_journal_context(
        self,
        patient_context: PatientContext,
        user_query: str,
        max_journals: int = 3
    ) -> List[str]:
        """Get relevant journal context for a patient diagnostic session."""
        
        # Create embedding for the query
        query_embedding = await embed(user_query)
        
        # Get relevant journals for this patient and query
        if hasattr(self.vector_store, 'get_relevant_journals_for_diagnosis'):
            journal_results = await self.vector_store.get_relevant_journals_for_diagnosis(
                query_embedding=query_embedding,
                patient_id=patient_context.patient_id,
                top_k=max_journals
            )
        else:
            # Fallback to regular similarity search
            journal_results = await self.vector_store.search_similar_journals(
                query_embedding=query_embedding,
                patient_id=patient_context.patient_id,
                top_k=max_journals
            )
        
        # Format journal context for LLM consumption
        formatted_contexts = []
        for embedding_id, similarity, content_data in journal_results:
            formatted_context = self._format_journal_for_diagnosis(content_data, similarity)
            if formatted_context:
                formatted_contexts.append(formatted_context)
        
        return formatted_contexts

    async def get_patient_journal_summary(self, patient_id: str) -> Dict[str, Any]:
        """Get a summary of all journals for a patient."""
        
        if hasattr(self.vector_store, 'get_journal_summary_for_patient'):
            return await self.vector_store.get_journal_summary_for_patient(patient_id)
        
        # Fallback implementation
        if hasattr(self.vector_store, 'get_patient_journal_embeddings'):
            embeddings = await self.vector_store.get_patient_journal_embeddings(patient_id)
            
            summary = {
                "total_journals": len(embeddings),
                "journal_types": {},
                "recent_journals": [],
                "key_topics": []
            }
            
            for embedding_id, data in embeddings[:5]:  # Limit to recent 5
                content = data.get('content', {})
                metadata = data.get('metadata', {})
                
                journal_type = metadata.get('journal_type', 'unknown')
                summary['journal_types'][journal_type] = summary['journal_types'].get(journal_type, 0) + 1
                
                summary['recent_journals'].append({
                    'title': content.get('title', 'Untitled'),
                    'type': journal_type,
                    'uploaded_at': metadata.get('uploaded_at')
                })
            
            return summary
        
        return {"total_journals": 0, "journal_types": {}, "recent_journals": [], "key_topics": []}

    async def search_journals_by_medical_condition(
        self,
        condition: str,
        patient_id: Optional[str] = None,
        max_results: int = 5
    ) -> List[Dict[str, Any]]:
        """Search for journals related to a specific medical condition."""
        
        # Create search query focused on the medical condition
        search_query = f"medical condition {condition} diagnosis treatment symptoms"
        query_embedding = await embed(search_query)
        
        # Search for relevant journals
        if hasattr(self.vector_store, 'search_similar_journals'):
            results = await self.vector_store.search_similar_journals(
                query_embedding=query_embedding,
                patient_id=patient_id,
                top_k=max_results
            )
        else:
            # Fallback to general similarity search
            filter_conditions = {"namespace": "journal"}
            if patient_id:
                filter_conditions["patient_id"] = patient_id
            
            results = await self.vector_store.similarity_search(
                embedding=query_embedding,
                top_k=max_results,
                filter_=filter_conditions
            )
        
        # Format results
        formatted_results = []
        for embedding_id, similarity, content_data in results:
            if isinstance(content_data, str):
                try:
                    content_data = json.loads(content_data)
                except json.JSONDecodeError:
                    continue
            
            if isinstance(content_data, dict) and 'content' in content_data:
                content = content_data['content']
                formatted_results.append({
                    'title': content.get('title', 'Untitled'),
                    'journal_type': content.get('journal_type', 'unknown'),
                    'summary': content.get('interpretation_summary', '')[:200] + "..." if content.get('interpretation_summary') else '',
                    'similarity_score': similarity,
                    'key_findings': content.get('key_findings', [])[:3]  # Limit to 3 findings
                })
        
        return formatted_results

    def _format_journal_for_diagnosis(self, content_data: Dict[str, Any], similarity_score: float) -> Optional[str]:
        """Format journal content for diagnostic reasoning context."""
        
        try:
            if isinstance(content_data, str):
                content_data = json.loads(content_data)
            
            if not isinstance(content_data, dict) or 'content' not in content_data:
                return None
            
            content = content_data['content']
            
            # Build formatted context
            context_parts = []
            
            # Journal header with relevance
            relevance = "High" if similarity_score > 0.8 else "Medium" if similarity_score > 0.6 else "Low"
            context_parts.append(f"📄 Journal Entry (Relevance: {relevance})")
            
            # Title and type
            if 'title' in content:
                context_parts.append(f"Title: {content['title']}")
            
            if 'journal_type' in content:
                context_parts.append(f"Type: {content['journal_type']}")
            
            # AI interpretation summary
            if 'interpretation_summary' in content:
                summary = content['interpretation_summary']
                if len(summary) > 300:
                    summary = summary[:300] + "..."
                context_parts.append(f"AI Summary: {summary}")
            
            # Key findings
            if 'key_findings' in content and content['key_findings']:
                findings = content['key_findings'][:3]  # Limit to top 3 findings
                context_parts.append(f"Key Findings: {'; '.join(findings)}")
            
            # Clinical significance
            if 'clinical_significance' in content:
                significance = content['clinical_significance']
                if len(significance) > 200:
                    significance = significance[:200] + "..."
                context_parts.append(f"Clinical Significance: {significance}")
            
            # Content excerpt for additional context
            if 'content' in content and isinstance(content['content'], str):
                excerpt = content['content'][:250] + "..." if len(content['content']) > 250 else content['content']
                context_parts.append(f"Excerpt: {excerpt}")
            
            return "\n".join(context_parts)
            
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Error formatting journal context: {e}")
            return None

    async def get_journal_insights_for_symptoms(
        self,
        symptoms: List[str],
        patient_id: Optional[str] = None
    ) -> Dict[str, List[str]]:
        """Get journal insights organized by symptom."""
        
        insights_by_symptom = {}
        
        for symptom in symptoms:
            # Search for journals related to this symptom
            search_query = f"symptom {symptom} diagnosis treatment causes"
            query_embedding = await embed(search_query)
            
            # Get relevant journals
            if hasattr(self.vector_store, 'search_similar_journals'):
                results = await self.vector_store.search_similar_journals(
                    query_embedding=query_embedding,
                    patient_id=patient_id,
                    top_k=2  # Limit to 2 most relevant per symptom
                )
            else:
                filter_conditions = {"namespace": "journal"}
                if patient_id:
                    filter_conditions["patient_id"] = patient_id
                
                results = await self.vector_store.similarity_search(
                    embedding=query_embedding,
                    top_k=2,
                    filter_=filter_conditions
                )
            
            # Extract insights for this symptom
            symptom_insights = []
            for embedding_id, similarity, content_data in results:
                if similarity > 0.6:  # Only include reasonably relevant results
                    insight = self._extract_symptom_insight(content_data, symptom)
                    if insight:
                        symptom_insights.append(insight)
            
            if symptom_insights:
                insights_by_symptom[symptom] = symptom_insights
        
        return insights_by_symptom

    def _extract_symptom_insight(self, content_data: Dict[str, Any], symptom: str) -> Optional[str]:
        """Extract specific insight about a symptom from journal content."""
        
        try:
            if isinstance(content_data, str):
                content_data = json.loads(content_data)
            
            if not isinstance(content_data, dict) or 'content' not in content_data:
                return None
            
            content = content_data['content']
            
            # Look for relevant information about the symptom
            insight_parts = []
            
            if 'title' in content:
                insight_parts.append(f"From '{content['title']}':")
            
            # Check interpretation summary for symptom mentions
            if 'interpretation_summary' in content:
                summary = content['interpretation_summary'].lower()
                if symptom.lower() in summary:
                    # Extract sentence containing the symptom
                    sentences = content['interpretation_summary'].split('.')
                    for sentence in sentences:
                        if symptom.lower() in sentence.lower():
                            insight_parts.append(sentence.strip())
                            break
            
            # Check key findings
            if 'key_findings' in content:
                for finding in content['key_findings']:
                    if symptom.lower() in finding.lower():
                        insight_parts.append(finding)
                        break
            
            return " ".join(insight_parts) if insight_parts else None
            
        except (json.JSONDecodeError, KeyError, TypeError):
            return None
