"""
Journal Context Service

This service provides context and summary information about journals
for use in patient care workflows and AI-assisted diagnosis.
"""

from __future__ import annotations
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from optimed.core.domain import (
    JournalEntry, JournalContext, JournalSearchQuery, JournalType
)
from .ehr_journal_sync_service import EHRJournalSyncService


class JournalContextService:
    """Service for providing journal context in clinical workflows."""
    
    def __init__(self, ehr_sync_service: EHRJournalSyncService):
        self.ehr_sync_service = ehr_sync_service
    
    async def get_patient_journal_summary(self, patient_id: str) -> Dict[str, Any]:
        """
        Get a comprehensive journal summary for a patient.
        
        Args:
            patient_id: The patient ID.
        
        Returns:
            Dictionary with journal summary information.
        """
        try:
            # Get journal context
            context = await self.ehr_sync_service.get_journal_context(patient_id)
            
            # Get recent notes for detailed view
            recent_notes = await self.ehr_sync_service.get_recent_journal_notes(patient_id, limit=5)
            
            return {
                "patient_id": patient_id,
                "summary": {
                    "total_journals": context.total_journals,
                    "journal_types": context.journal_types,
                    "key_topics": context.key_topics,
                    "last_updated": context.last_updated.isoformat() if context.last_updated else None
                },
                "recent_notes": [
                    {
                        "journal_id": note.journal_id,
                        "title": note.title,
                        "type": note.journal_type.value,
                        "source": note.source,
                        "uploaded_at": note.uploaded_at.isoformat() if note.uploaded_at else None,
                        "word_count": note.word_count,
                        "has_interpretation": note.has_interpretation,
                        "content_preview": note.content[:150] + "..." if len(note.content) > 150 else note.content
                    }
                    for note in recent_notes
                ],
                "statistics": {
                    "ehr_sourced": len([n for n in recent_notes if n.is_from_ehr]),
                    "uploaded": len([n for n in recent_notes if not n.is_from_ehr]),
                    "with_interpretation": len([n for n in recent_notes if n.has_interpretation])
                }
            }
            
        except Exception as e:
            print(f"Error getting journal summary for patient {patient_id}: {e}")
            return {
                "patient_id": patient_id,
                "summary": {
                    "total_journals": 0,
                    "journal_types": {},
                    "key_topics": [],
                    "last_updated": None
                },
                "recent_notes": [],
                "statistics": {
                    "ehr_sourced": 0,
                    "uploaded": 0,
                    "with_interpretation": 0
                },
                "error": str(e)
            }
    
    async def get_relevant_journals_for_query(
        self, 
        patient_id: str, 
        query: str, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get journals relevant to a specific clinical query.
        
        Args:
            patient_id: The patient ID.
            query: Clinical query or symptoms.
            limit: Maximum number of relevant journals.
        
        Returns:
            List of relevant journal entries with relevance scores.
        """
        try:
            # Search for relevant journals
            search_query = JournalSearchQuery(
                patient_id=patient_id,
                query=query,
                limit=limit
            )
            
            relevant_notes = await self.ehr_sync_service.search_journal_notes(search_query)
            
            # Format for clinical context
            formatted_journals = []
            for note in relevant_notes:
                relevance_score = self._calculate_relevance_score(note, query)
                
                formatted_journals.append({
                    "journal_id": note.journal_id,
                    "title": note.title,
                    "type": note.journal_type.value,
                    "source": note.source,
                    "relevance_score": relevance_score,
                    "relevance_level": self._get_relevance_level(relevance_score),
                    "uploaded_at": note.uploaded_at.isoformat() if note.uploaded_at else None,
                    "content_preview": note.content[:200] + "..." if len(note.content) > 200 else note.content,
                    "clinical_context": self._generate_clinical_context(note, query),
                    "has_interpretation": note.has_interpretation
                })
            
            return formatted_journals
            
        except Exception as e:
            print(f"Error getting relevant journals for query '{query}': {e}")
            return []
    
    async def get_journals_by_type(
        self, 
        patient_id: str, 
        journal_type: JournalType,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get journals of a specific type for a patient.
        
        Args:
            patient_id: The patient ID.
            journal_type: Type of journals to retrieve.
            limit: Maximum number of journals.
        
        Returns:
            List of journals of the specified type.
        """
        try:
            search_query = JournalSearchQuery(
                patient_id=patient_id,
                journal_type=journal_type,
                limit=limit
            )
            
            journals = await self.ehr_sync_service.search_journal_notes(search_query)
            
            return [
                {
                    "journal_id": note.journal_id,
                    "title": note.title,
                    "type": note.journal_type.value,
                    "source": note.source,
                    "uploaded_at": note.uploaded_at.isoformat() if note.uploaded_at else None,
                    "word_count": note.word_count,
                    "has_interpretation": note.has_interpretation,
                    "content_preview": note.content[:150] + "..." if len(note.content) > 150 else note.content
                }
                for note in journals
            ]
            
        except Exception as e:
            print(f"Error getting journals of type {journal_type.value}: {e}")
            return []
    
    async def get_journal_timeline(self, patient_id: str) -> List[Dict[str, Any]]:
        """
        Get a chronological timeline of journal entries for a patient.
        
        Args:
            patient_id: The patient ID.
        
        Returns:
            List of journal entries in chronological order.
        """
        try:
            # Get all journals for the patient
            all_notes = await self.ehr_sync_service.get_patient_journal_notes(patient_id)
            
            # Sort by date (oldest first for timeline)
            timeline_notes = sorted(
                all_notes,
                key=lambda x: x.uploaded_at or datetime.min.replace(tzinfo=timezone.utc)
            )
            
            # Format for timeline display
            timeline = []
            for note in timeline_notes:
                timeline.append({
                    "journal_id": note.journal_id,
                    "title": note.title,
                    "type": note.journal_type.value,
                    "source": note.source,
                    "date": note.uploaded_at.isoformat() if note.uploaded_at else None,
                    "date_display": note.uploaded_at.strftime("%Y-%m-%d %H:%M") if note.uploaded_at else "Unknown",
                    "content_preview": note.content[:100] + "..." if len(note.content) > 100 else note.content,
                    "has_interpretation": note.has_interpretation
                })
            
            return timeline
            
        except Exception as e:
            print(f"Error getting journal timeline for patient {patient_id}: {e}")
            return []
    
    def _calculate_relevance_score(self, note: JournalEntry, query: str) -> float:
        """Calculate relevance score between a journal note and query."""
        query_words = set(query.lower().split())
        
        # Check title relevance
        title_words = set(note.title.lower().split())
        title_matches = len(query_words.intersection(title_words))
        title_score = title_matches / len(query_words) if query_words else 0
        
        # Check content relevance
        content_words = set(note.content.lower().split())
        content_matches = len(query_words.intersection(content_words))
        content_score = content_matches / len(query_words) if query_words else 0
        
        # Weighted score (title weighted more heavily)
        relevance_score = (title_score * 0.7) + (content_score * 0.3)
        
        return min(relevance_score, 1.0)  # Cap at 1.0
    
    def _get_relevance_level(self, score: float) -> str:
        """Convert relevance score to human-readable level."""
        if score >= 0.7:
            return "High"
        elif score >= 0.4:
            return "Medium"
        elif score >= 0.1:
            return "Low"
        else:
            return "Minimal"
    
    def _generate_clinical_context(self, note: JournalEntry, query: str) -> str:
        """Generate clinical context explanation for a journal note."""
        relevance_level = self._get_relevance_level(
            self._calculate_relevance_score(note, query)
        )
        
        return f"""Clinical Context (Relevance: {relevance_level})
Document Type: {note.journal_type.value.replace('_', ' ').title()}
Source: {note.source}
Key Information: Contains relevant information related to "{query}"
Clinical Value: Provides context for current patient assessment and care planning"""
