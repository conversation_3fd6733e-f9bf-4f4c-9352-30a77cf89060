"""
EHR Journal Sync Service

This service fetches journal notes from the EHR system using FHIR API
and converts them to domain objects following hexagonal architecture.
"""

from __future__ import annotations
import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import uuid

from optimed.core.domain import (
    JournalEntry, JournalType, ProcessingStatus, DocumentMetadata, DocumentFormat,
    JournalContext, JournalSearchQuery, JournalDomainError
)
from optimed.core.ports import FHIRRepository


class EHRJournalSyncService:
    """Service to fetch and sync journal notes from EHR system."""
    
    def __init__(self, fhir_repository: FHIRRepository):
        self.fhir_repository = fhir_repository
    
    async def get_patient_journal_notes(self, patient_id: str) -> List[JournalEntry]:
        """
        Fetch journal notes for a specific patient from the EHR system.
        
        Args:
            patient_id: The patient ID to fetch notes for.
        
        Returns:
            List of journal entries converted from EHR clinical notes.
        """
        try:
            # Check if FHIR repository supports clinical notes
            if not hasattr(self.fhir_repository, 'get_patient_clinical_notes'):
                print(f"FHIR repository does not support clinical notes extraction")
                return []
            
            # Fetch clinical notes from EHR via FHIR
            clinical_notes = await self.fhir_repository.get_patient_clinical_notes(patient_id)
            
            if not clinical_notes:
                print(f"No clinical notes found for patient {patient_id}")
                return []
            
            # Convert FHIR resources to domain objects
            journal_entries = []
            for note in clinical_notes:
                try:
                    # Ensure the note has required fields
                    if not note.content or not note.title:
                        continue
                    
                    # Create journal entry from clinical note
                    journal_entry = JournalEntry(
                        journal_id=note.journal_id or f"ehr_{uuid.uuid4().hex[:8]}",
                        patient_id=patient_id,
                        title=note.title,
                        content=note.content,
                        journal_type=note.journal_type or JournalType.PROGRESS_NOTE,
                        metadata=note.metadata or self._create_default_metadata(note),
                        processing_status=ProcessingStatus.PENDING,
                        uploaded_at=note.uploaded_at or datetime.now(timezone.utc),
                        source="EHR"
                    )
                    
                    journal_entries.append(journal_entry)
                    
                except Exception as e:
                    print(f"Error converting clinical note to journal entry: {e}")
                    continue
            
            print(f"Successfully converted {len(journal_entries)} clinical notes for patient {patient_id}")
            return journal_entries
            
        except Exception as e:
            print(f"Error fetching journal notes for patient {patient_id}: {e}")
            raise JournalDomainError(f"Failed to fetch journal notes: {str(e)}")
    
    async def get_recent_journal_notes(self, patient_id: str, limit: int = 10) -> List[JournalEntry]:
        """
        Get recent journal notes for a patient, sorted by date.
        
        Args:
            patient_id: The patient ID.
            limit: Maximum number of notes to return.
        
        Returns:
            List of recent journal entries.
        """
        try:
            all_notes = await self.get_patient_journal_notes(patient_id)
            
            # Sort by uploaded date (most recent first)
            sorted_notes = sorted(
                all_notes, 
                key=lambda x: x.uploaded_at or datetime.min.replace(tzinfo=timezone.utc), 
                reverse=True
            )
            
            return sorted_notes[:limit]
            
        except Exception as e:
            print(f"Error fetching recent journal notes for patient {patient_id}: {e}")
            return []
    
    async def search_journal_notes(self, query: JournalSearchQuery) -> List[JournalEntry]:
        """
        Search journal notes based on query criteria.
        
        Args:
            query: Search query with filters.
        
        Returns:
            List of matching journal entries.
        """
        try:
            if not query.patient_id:
                return []
            
            # Get all notes for the patient
            all_notes = await self.get_patient_journal_notes(query.patient_id)
            
            # Apply filters
            filtered_notes = all_notes
            
            # Filter by journal type
            if query.journal_type:
                filtered_notes = [n for n in filtered_notes if n.journal_type == query.journal_type]
            
            # Filter by processing status
            if query.processing_status:
                filtered_notes = [n for n in filtered_notes if n.processing_status == query.processing_status]
            
            # Filter by date range
            if query.date_from:
                filtered_notes = [n for n in filtered_notes if n.uploaded_at and n.uploaded_at >= query.date_from]
            
            if query.date_to:
                filtered_notes = [n for n in filtered_notes if n.uploaded_at and n.uploaded_at <= query.date_to]
            
            # Filter by text query
            if query.query:
                search_term = query.query.lower()
                filtered_notes = [
                    n for n in filtered_notes 
                    if search_term in n.title.lower() or search_term in n.content.lower()
                ]
            
            # Filter by source
            if query.source:
                filtered_notes = [n for n in filtered_notes if n.source == query.source]
            
            # Sort by date (most recent first)
            filtered_notes.sort(
                key=lambda x: x.uploaded_at or datetime.min.replace(tzinfo=timezone.utc), 
                reverse=True
            )
            
            # Apply pagination
            start_idx = query.offset
            end_idx = start_idx + query.limit
            
            return filtered_notes[start_idx:end_idx]
            
        except Exception as e:
            print(f"Error searching journal notes: {e}")
            return []
    
    async def get_journal_context(self, patient_id: str, query: Optional[str] = None) -> JournalContext:
        """
        Get journal context summary for a patient.
        
        Args:
            patient_id: The patient ID.
            query: Optional query to find relevant journals.
        
        Returns:
            Journal context with summary and relevant journals.
        """
        try:
            # Get all journal notes for the patient
            all_notes = await self.get_patient_journal_notes(patient_id)
            
            # Calculate summary statistics
            total_journals = len(all_notes)
            journal_types = {}
            for note in all_notes:
                type_name = note.journal_type.value
                journal_types[type_name] = journal_types.get(type_name, 0) + 1
            
            # Get recent journals (last 5)
            recent_notes = sorted(
                all_notes, 
                key=lambda x: x.uploaded_at or datetime.min.replace(tzinfo=timezone.utc), 
                reverse=True
            )[:5]
            
            recent_journals = [
                {
                    "title": note.title,
                    "type": note.journal_type.value,
                    "uploaded_at": note.uploaded_at.isoformat() if note.uploaded_at else None,
                    "has_interpretation": note.has_interpretation,
                    "source": note.source
                }
                for note in recent_notes
            ]
            
            # Extract key topics from titles and content
            key_topics = self._extract_key_topics(all_notes)
            
            # Find relevant journals if query provided
            relevant_journals = []
            if query:
                search_query = JournalSearchQuery(
                    patient_id=patient_id,
                    query=query,
                    limit=5
                )
                relevant_notes = await self.search_journal_notes(search_query)
                
                relevant_journals = [
                    {
                        "context": self._format_journal_context(note),
                        "relevance": "Based on query similarity"
                    }
                    for note in relevant_notes
                ]
            
            return JournalContext(
                patient_id=patient_id,
                total_journals=total_journals,
                journal_types=journal_types,
                recent_journals=recent_journals,
                key_topics=key_topics,
                relevant_journals=relevant_journals,
                query_used=query,
                last_updated=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            print(f"Error getting journal context for patient {patient_id}: {e}")
            # Return empty context on error
            return JournalContext(
                patient_id=patient_id,
                total_journals=0,
                journal_types={},
                recent_journals=[],
                key_topics=[],
                relevant_journals=[],
                query_used=query,
                last_updated=datetime.now(timezone.utc)
            )
    
    def _create_default_metadata(self, note: Any) -> DocumentMetadata:
        """Create default metadata for a clinical note."""
        content_length = len(note.content) if hasattr(note, 'content') and note.content else 0
        
        return DocumentMetadata(
            file_name=f"clinical_note_{uuid.uuid4().hex[:8]}.txt",
            file_size=content_length,
            format=DocumentFormat.TXT,
            mime_type="text/plain",
            word_count=len(note.content.split()) if hasattr(note, 'content') and note.content else 0,
            created_at=datetime.now(timezone.utc),
            title=getattr(note, 'title', 'Clinical Note')
        )
    
    def _extract_key_topics(self, notes: List[JournalEntry]) -> List[str]:
        """Extract key topics from journal notes."""
        # Simple keyword extraction from titles
        topics = set()
        
        for note in notes:
            # Extract words from title
            title_words = note.title.lower().split()
            for word in title_words:
                # Filter out common words and keep medical terms
                if len(word) > 3 and word not in ['the', 'and', 'for', 'with', 'from', 'this', 'that']:
                    topics.add(word)
        
        # Return most common topics (limit to 10)
        return list(topics)[:10]
    
    def _format_journal_context(self, note: JournalEntry) -> str:
        """Format journal note for context display."""
        return f"""📄 Journal Entry (Relevance: High)
Title: {note.title}
Type: {note.journal_type.value}
Source: {note.source}
Content Preview: {note.content[:200]}...
Clinical Significance: Relevant to current patient assessment"""
