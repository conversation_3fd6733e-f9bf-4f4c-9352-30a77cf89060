"""
EHR Journal Sync Service

This service automatically fetches clinical notes, discharge summaries, and progress notes
from the EHR (via FHIR) and creates journal entries for AI analysis.
"""

from __future__ import annotations
import asyncio
from typing import List, Optional
from datetime import datetime, timezone

from optimed.core.domain import JournalEntry, ProcessingStatus
from optimed.core.ports import FHIRRepository, JournalRepository, JournalProcessor
from optimed.adapters.fhir_hapi.repository import HAPIFHIRRepository


class EHRJournalSyncService:
    """Service to sync clinical notes from EHR to journal system."""
    
    def __init__(
        self,
        fhir_repository: FHIRRepository,
        journal_repository: JournalRepository,
        journal_processor: Optional[JournalProcessor] = None
    ):
        self.fhir_repository = fhir_repository
        self.journal_repository = journal_repository
        self.journal_processor = journal_processor
    
    async def sync_patient_clinical_notes(self, patient_id: str) -> List[JournalEntry]:
        """
        Sync clinical notes for a specific patient from EHR to journal system.
        
        Args:
            patient_id: The patient ID to sync notes for
            
        Returns:
            List of created journal entries
        """
        try:
            # Check if FHIR repository supports clinical notes
            if not hasattr(self.fhir_repository, 'get_patient_clinical_notes'):
                print(f"FHIR repository does not support clinical notes extraction")
                return []
            
            # Fetch clinical notes from EHR
            clinical_notes = await self.fhir_repository.get_patient_clinical_notes(patient_id)
            
            if not clinical_notes:
                print(f"No clinical notes found for patient {patient_id}")
                return []
            
            created_entries = []
            
            for note in clinical_notes:
                try:
                    # Check if this journal entry already exists
                    existing_entry = await self.journal_repository.get_journal(note.journal_id)
                    if existing_entry:
                        print(f"Journal entry {note.journal_id} already exists, skipping")
                        continue
                    
                    # Save the journal entry
                    saved_entry = await self.journal_repository.save_journal(note)
                    created_entries.append(saved_entry)
                    
                    # Process the journal entry if processor is available
                    if self.journal_processor:
                        try:
                            await self.journal_processor.process_journal_entry(saved_entry)
                            print(f"Processed journal entry: {saved_entry.journal_id}")
                        except Exception as e:
                            print(f"Error processing journal entry {saved_entry.journal_id}: {e}")
                    
                    print(f"Created journal entry: {saved_entry.title}")
                    
                except Exception as e:
                    print(f"Error creating journal entry for {note.journal_id}: {e}")
                    continue
            
            print(f"Successfully synced {len(created_entries)} clinical notes for patient {patient_id}")
            return created_entries
            
        except Exception as e:
            print(f"Error syncing clinical notes for patient {patient_id}: {e}")
            return []
    
    async def sync_all_patients_clinical_notes(self, patient_ids: List[str]) -> dict[str, List[JournalEntry]]:
        """
        Sync clinical notes for multiple patients.
        
        Args:
            patient_ids: List of patient IDs to sync
            
        Returns:
            Dictionary mapping patient_id to list of created journal entries
        """
        results = {}
        
        for patient_id in patient_ids:
            try:
                entries = await self.sync_patient_clinical_notes(patient_id)
                results[patient_id] = entries
                
                # Add small delay to avoid overwhelming the EHR system
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"Error syncing patient {patient_id}: {e}")
                results[patient_id] = []
        
        total_entries = sum(len(entries) for entries in results.values())
        print(f"Synced {total_entries} total clinical notes across {len(patient_ids)} patients")
        
        return results
    
    async def get_sync_status(self, patient_id: str) -> dict:
        """
        Get sync status for a patient's clinical notes.
        
        Args:
            patient_id: The patient ID to check
            
        Returns:
            Dictionary with sync status information
        """
        try:
            # Get existing journal entries for this patient
            existing_journals = await self.journal_repository.get_patient_journals(patient_id)
            
            # Filter for EHR-sourced journals (those with journal_id starting with 'fhir_')
            ehr_journals = [j for j in existing_journals if j.journal_id.startswith('fhir_')]
            
            # Get available clinical notes from EHR
            available_notes = []
            if hasattr(self.fhir_repository, 'get_patient_clinical_notes'):
                available_notes = await self.fhir_repository.get_patient_clinical_notes(patient_id)
            
            return {
                'patient_id': patient_id,
                'total_ehr_journals': len(ehr_journals),
                'available_clinical_notes': len(available_notes),
                'last_sync': max([j.uploaded_at for j in ehr_journals]) if ehr_journals else None,
                'pending_sync': len(available_notes) - len(ehr_journals),
                'ehr_journals': [
                    {
                        'journal_id': j.journal_id,
                        'title': j.title,
                        'journal_type': j.journal_type.value,
                        'uploaded_at': j.uploaded_at.isoformat(),
                        'processing_status': j.processing_status.value
                    }
                    for j in ehr_journals
                ]
            }
            
        except Exception as e:
            print(f"Error getting sync status for patient {patient_id}: {e}")
            return {
                'patient_id': patient_id,
                'error': str(e)
            }
