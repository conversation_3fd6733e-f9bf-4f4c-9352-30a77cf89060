#!/usr/bin/env python3
"""
Demo script showing how doctors can integrate with journal notes
and how the LLM can see and use journal context during patient consultations.
"""

import asyncio
import json
from datetime import datetime, timezone

# This would be your actual API client in production
class OptiMedAPIClient:
    """Mock API client for demonstration purposes."""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_token = None
    
    async def login(self, username: str, password: str):
        """Login and get session token."""
        print(f"🔐 Logging in as {username}...")
        # Mock login - in real implementation, this would make HTTP request
        self.session_token = "mock_session_token_123"
        print("✅ Login successful")
    
    async def upload_journal(self, file_path: str, patient_id: str = None, title: str = None):
        """Upload a journal document for processing."""
        print(f"📄 Uploading journal: {file_path}")
        print(f"   Patient ID: {patient_id}")
        print(f"   Title: {title}")
        
        # Mock response
        return {
            "result_id": "result_123",
            "success": True,
            "processing_time_seconds": 15.3,
            "journal": {
                "journal_id": "journal_456",
                "title": title or "Hypertension Research Study",
                "journal_type": "RESEARCH_PAPER",
                "processing_status": "COMPLETED",
                "has_interpretation": True,
                "word_count": 2500
            }
        }
    
    async def get_patient_journal_context(self, patient_id: str, query: str = None):
        """Get journal context for a patient."""
        print(f"📚 Getting journal context for patient {patient_id}")
        if query:
            print(f"   Query: {query}")
        
        # Mock response
        return {
            "patient_id": patient_id,
            "journal_summary": {
                "total_journals": 3,
                "journal_types": {
                    "RESEARCH_PAPER": 2,
                    "CASE_STUDY": 1
                },
                "recent_journals": [
                    {
                        "title": "ACE Inhibitors in Elderly Hypertension",
                        "type": "RESEARCH_PAPER",
                        "uploaded_at": "2024-01-15T10:30:00Z",
                        "has_interpretation": True
                    },
                    {
                        "title": "Case Study: Resistant Hypertension",
                        "type": "CASE_STUDY", 
                        "uploaded_at": "2024-01-10T14:20:00Z",
                        "has_interpretation": True
                    }
                ],
                "key_topics": ["hypertension", "ACE inhibitors", "elderly patients", "blood pressure management"]
            },
            "relevant_journals": [
                {
                    "context": "📄 Journal Entry (Relevance: High)\nTitle: ACE Inhibitors in Elderly Hypertension\nType: RESEARCH_PAPER\nAI Summary: This study demonstrates that ACE inhibitors are highly effective in treating hypertension in patients over 65, with minimal side effects...",
                    "relevance": "Based on query similarity"
                }
            ] if query else []
        }
    
    async def diagnose_with_journal_insights(self, patient_id: str, user_prompt: str, include_journal_context: bool = True):
        """Run diagnosis with journal context integration."""
        print(f"🩺 Running diagnosis for patient {patient_id}")
        print(f"   Prompt: {user_prompt}")
        print(f"   Include journal context: {include_journal_context}")
        
        # Mock response showing how journal context influences diagnosis
        return {
            "patient_id": patient_id,
            "primary_icd": "I10",
            "differential": ["I15.9", "I25.10"],
            "confidence": 0.87,
            "explanation": """Based on the patient's clinical presentation and relevant journal evidence:

**Clinical Assessment:**
- Blood pressure readings consistently above 140/90 mmHg
- Patient age 68 years with history of smoking
- No signs of secondary hypertension

**Journal-Informed Insights:**
From "ACE Inhibitors in Elderly Hypertension" (Research Paper):
- Study shows 85% efficacy in patients >65 years
- Minimal side effects compared to other antihypertensives
- Recommended as first-line therapy for elderly patients

From "Case Study: Resistant Hypertension":
- Similar presentation in 67-year-old patient
- Successful treatment with ACE inhibitor + lifestyle modifications

**Recommendation:**
Primary diagnosis: Essential Hypertension (I10)
Treatment: Initiate ACE inhibitor therapy based on journal evidence showing high efficacy and safety in elderly patients.""",
            
            "journal_context": {
                "summary": {
                    "total_journals": 3,
                    "available_journals": 3,
                    "context_used_in_diagnosis": True
                },
                "journal_types": {
                    "RESEARCH_PAPER": 2,
                    "CASE_STUDY": 1
                },
                "relevant_to_query": [
                    {
                        "context_snippet": "Study demonstrates 85% efficacy of ACE inhibitors in elderly hypertension patients with minimal side effects..."
                    },
                    {
                        "context_snippet": "Case study shows successful treatment of resistant hypertension in 67-year-old patient using ACE inhibitor therapy..."
                    }
                ]
            },
            
            # This metadata shows which journals influenced the response
            "metadata": {
                "journal_context_used": True,
                "journal_references": [
                    {
                        "journal_id": "journal_456",
                        "title": "ACE Inhibitors in Elderly Hypertension",
                        "journal_type": "RESEARCH_PAPER",
                        "similarity_score": 0.89,
                        "key_findings": ["85% efficacy in elderly", "minimal side effects"]
                    },
                    {
                        "journal_id": "journal_789",
                        "title": "Case Study: Resistant Hypertension", 
                        "journal_type": "CASE_STUDY",
                        "similarity_score": 0.76,
                        "key_findings": ["successful ACE inhibitor treatment", "lifestyle modifications"]
                    }
                ],
                "num_journals_referenced": 2
            }
        }


async def demo_journal_integration():
    """Demonstrate the complete journal integration workflow."""
    
    print("🏥 OptiMed Journal Integration Demo")
    print("=" * 50)
    
    # Initialize API client
    client = OptiMedAPIClient()
    
    # Step 1: Doctor logs in
    await client.login("dr.smith", "password123")
    print()
    
    # Step 2: Upload medical journals for a patient
    print("📚 Step 1: Uploading Medical Journals")
    print("-" * 30)
    
    patient_id = "patient_12345"
    
    # Upload research paper
    result1 = await client.upload_journal(
        file_path="research_papers/ace_inhibitors_elderly.pdf",
        patient_id=patient_id,
        title="ACE Inhibitors in Elderly Hypertension"
    )
    print(f"✅ Uploaded: {result1['journal']['title']}")
    
    # Upload case study
    result2 = await client.upload_journal(
        file_path="case_studies/resistant_hypertension.pdf", 
        patient_id=patient_id,
        title="Case Study: Resistant Hypertension"
    )
    print(f"✅ Uploaded: {result2['journal']['title']}")
    print()
    
    # Step 3: View patient's journal context
    print("📖 Step 2: Viewing Patient Journal Context")
    print("-" * 40)
    
    journal_context = await client.get_patient_journal_context(patient_id)
    print(f"📊 Total journals: {journal_context['journal_summary']['total_journals']}")
    print(f"📋 Journal types: {journal_context['journal_summary']['journal_types']}")
    print(f"🔑 Key topics: {', '.join(journal_context['journal_summary']['key_topics'])}")
    print()
    
    # Step 4: Search for relevant journals
    print("🔍 Step 3: Searching Relevant Journals")
    print("-" * 35)
    
    search_context = await client.get_patient_journal_context(
        patient_id, 
        query="hypertension treatment elderly patients"
    )
    
    print("📄 Relevant journals found:")
    for journal in search_context['relevant_journals']:
        print(f"   • {journal['context'][:100]}...")
    print()
    
    # Step 5: Run diagnosis with journal integration
    print("🩺 Step 4: AI Diagnosis with Journal Integration")
    print("-" * 45)
    
    diagnosis = await client.diagnose_with_journal_insights(
        patient_id=patient_id,
        user_prompt="68-year-old patient with elevated blood pressure 150/95, history of smoking. What's the diagnosis and recommended treatment?",
        include_journal_context=True
    )
    
    print("🎯 Diagnosis Results:")
    print(f"   Primary ICD: {diagnosis['primary_icd']}")
    print(f"   Confidence: {diagnosis['confidence']:.1%}")
    print(f"   Journal context used: {diagnosis['journal_context']['summary']['context_used_in_diagnosis']}")
    print(f"   Journals referenced: {diagnosis['metadata']['num_journals_referenced']}")
    print()
    
    print("📝 AI Explanation (with Journal Insights):")
    print(diagnosis['explanation'])
    print()
    
    print("📚 Journal References Used:")
    for ref in diagnosis['metadata']['journal_references']:
        print(f"   • {ref['title']} ({ref['journal_type']})")
        print(f"     Relevance: {ref['similarity_score']:.1%}")
        print(f"     Key findings: {', '.join(ref['key_findings'])}")
    print()
    
    # Step 6: Show comparison without journal context
    print("🔄 Step 5: Comparison Without Journal Context")
    print("-" * 42)
    
    diagnosis_no_journals = await client.diagnose_with_journal_insights(
        patient_id=patient_id,
        user_prompt="68-year-old patient with elevated blood pressure 150/95, history of smoking. What's the diagnosis and recommended treatment?",
        include_journal_context=False
    )
    
    print("🎯 Diagnosis without journals:")
    print("   • Would rely only on clinical data and general medical knowledge")
    print("   • No specific evidence from uploaded research papers")
    print("   • Less personalized treatment recommendations")
    print()
    
    print("✨ Benefits of Journal Integration:")
    print("   ✅ Evidence-based recommendations from uploaded research")
    print("   ✅ Patient-specific case studies inform treatment decisions")
    print("   ✅ Doctors can see which journals influenced AI reasoning")
    print("   ✅ Continuous learning from medical literature")
    print("   ✅ Improved diagnostic confidence and accuracy")


if __name__ == "__main__":
    print("Starting OptiMed Journal Integration Demo...")
    asyncio.run(demo_journal_integration())
