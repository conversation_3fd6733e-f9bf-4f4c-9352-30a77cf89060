-- Migration: Create journal processing tables
-- Description: Add tables for journal entries, interpretations, and related data

-- Create journals table
CREATE TABLE IF NOT EXISTS journals (
    journal_id VARCHAR(255) PRIMARY KEY,
    patient_id VARCHAR(255),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    
    -- Document content
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    journal_type VARCHAR(50) NOT NULL,
    processing_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    
    -- Timestamps
    uploaded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Source information
    source_url TEXT,
    doi VARCHAR(255),
    pmid VARCHAR(50),
    journal_name TEXT,
    publication_date TIMESTAMPTZ,
    
    -- Structured content
    authors JSONB DEFAULT '[]',
    abstract TEXT,
    sections JSONB DEFAULT '{}',
    references JSONB DEFAULT '[]',
    figures JSONB DEFAULT '[]',
    tables JSONB DEFAULT '[]',
    
    -- Document metadata
    metadata JSONB NOT NULL DEFAULT '{}'
);

-- Create indexes for journals table
CREATE INDEX IF NOT EXISTS idx_journals_patient_id ON journals(patient_id);
CREATE INDEX IF NOT EXISTS idx_journals_user_id ON journals(user_id);
CREATE INDEX IF NOT EXISTS idx_journals_journal_type ON journals(journal_type);
CREATE INDEX IF NOT EXISTS idx_journals_processing_status ON journals(processing_status);
CREATE INDEX IF NOT EXISTS idx_journals_uploaded_at ON journals(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_journals_title_gin ON journals USING gin(to_tsvector('english', title));
CREATE INDEX IF NOT EXISTS idx_journals_content_gin ON journals USING gin(to_tsvector('english', content));

-- Create journal_interpretations table
CREATE TABLE IF NOT EXISTS journal_interpretations (
    interpretation_id VARCHAR(255) PRIMARY KEY,
    journal_id VARCHAR(255) NOT NULL REFERENCES journals(journal_id) ON DELETE CASCADE,
    
    -- Summary and findings
    summary TEXT NOT NULL,
    key_findings JSONB DEFAULT '[]',
    medical_insights JSONB DEFAULT '[]',
    
    -- Clinical analysis
    clinical_significance TEXT NOT NULL,
    patient_applicability TEXT,
    treatment_implications JSONB DEFAULT '[]',
    diagnostic_implications JSONB DEFAULT '[]',
    
    -- Quality metrics
    overall_confidence DECIMAL(3,2) NOT NULL CHECK (overall_confidence >= 0 AND overall_confidence <= 1),
    confidence_level VARCHAR(20) NOT NULL,
    quality_score DECIMAL(3,2) NOT NULL CHECK (quality_score >= 0 AND quality_score <= 1),
    evidence_level VARCHAR(50),
    
    -- Processing metadata
    interpreted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    interpreted_by_model VARCHAR(100) NOT NULL,
    processing_time_seconds DECIMAL(10,3),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Extracted entities
    diseases JSONB DEFAULT '[]',
    medications JSONB DEFAULT '[]',
    procedures JSONB DEFAULT '[]',
    symptoms JSONB DEFAULT '[]',
    risk_factors JSONB DEFAULT '[]'
);

-- Create indexes for journal_interpretations table
CREATE INDEX IF NOT EXISTS idx_interpretations_journal_id ON journal_interpretations(journal_id);
CREATE INDEX IF NOT EXISTS idx_interpretations_confidence ON journal_interpretations(overall_confidence);
CREATE INDEX IF NOT EXISTS idx_interpretations_interpreted_at ON journal_interpretations(interpreted_at);
CREATE INDEX IF NOT EXISTS idx_interpretations_summary_gin ON journal_interpretations USING gin(to_tsvector('english', summary));

-- Create journal_processing_logs table for audit trail
CREATE TABLE IF NOT EXISTS journal_processing_logs (
    log_id SERIAL PRIMARY KEY,
    journal_id VARCHAR(255) NOT NULL,
    processing_step VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- SUCCESS, FAILED, WARNING
    message TEXT,
    processing_time_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for processing logs
CREATE INDEX IF NOT EXISTS idx_processing_logs_journal_id ON journal_processing_logs(journal_id);
CREATE INDEX IF NOT EXISTS idx_processing_logs_created_at ON journal_processing_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_processing_logs_status ON journal_processing_logs(status);

-- Create journal_embeddings table (if not using existing embeddings table)
-- This extends the existing embeddings table structure for journal-specific data
-- Note: This assumes the existing embeddings table from migration 001

-- Add journal-specific columns to embeddings table if they don't exist
DO $$
BEGIN
    -- Add journal_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'embeddings' AND column_name = 'journal_id') THEN
        ALTER TABLE embeddings ADD COLUMN journal_id VARCHAR(255);
        CREATE INDEX idx_embeddings_journal_id ON embeddings(journal_id);
    END IF;
    
    -- Add interpretation_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'embeddings' AND column_name = 'interpretation_id') THEN
        ALTER TABLE embeddings ADD COLUMN interpretation_id VARCHAR(255);
        CREATE INDEX idx_embeddings_interpretation_id ON embeddings(interpretation_id);
    END IF;
END $$;

-- Create view for journal summary with interpretation status
CREATE OR REPLACE VIEW journal_summary AS
SELECT 
    j.journal_id,
    j.patient_id,
    j.user_id,
    j.title,
    j.journal_type,
    j.processing_status,
    j.uploaded_at,
    j.processed_at,
    (j.metadata->>'word_count')::INTEGER as word_count,
    (j.metadata->>'file_size')::INTEGER as file_size,
    (j.metadata->>'format') as file_format,
    CASE WHEN ji.interpretation_id IS NOT NULL THEN true ELSE false END as has_interpretation,
    ji.overall_confidence,
    ji.confidence_level,
    ji.interpreted_at
FROM journals j
LEFT JOIN journal_interpretations ji ON j.journal_id = ji.journal_id;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_journals_updated_at ON journals;
CREATE TRIGGER update_journals_updated_at
    BEFORE UPDATE ON journals
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_interpretations_updated_at ON journal_interpretations;
CREATE TRIGGER update_interpretations_updated_at
    BEFORE UPDATE ON journal_interpretations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function for full-text search across journals
CREATE OR REPLACE FUNCTION search_journals(
    search_query TEXT,
    journal_type_filter VARCHAR(50) DEFAULT NULL,
    patient_id_filter VARCHAR(255) DEFAULT NULL,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    journal_id VARCHAR(255),
    title TEXT,
    journal_type VARCHAR(50),
    patient_id VARCHAR(255),
    uploaded_at TIMESTAMPTZ,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        j.journal_id,
        j.title,
        j.journal_type,
        j.patient_id,
        j.uploaded_at,
        ts_rank(
            to_tsvector('english', j.title || ' ' || j.content || ' ' || COALESCE(j.abstract, '')),
            plainto_tsquery('english', search_query)
        ) as rank
    FROM journals j
    WHERE 
        to_tsvector('english', j.title || ' ' || j.content || ' ' || COALESCE(j.abstract, '')) 
        @@ plainto_tsquery('english', search_query)
        AND (journal_type_filter IS NULL OR j.journal_type = journal_type_filter)
        AND (patient_id_filter IS NULL OR j.patient_id = patient_id_filter)
    ORDER BY rank DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions (adjust as needed for your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON journals TO optimed_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON journal_interpretations TO optimed_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON journal_processing_logs TO optimed_app;
-- GRANT USAGE, SELECT ON SEQUENCE journal_processing_logs_log_id_seq TO optimed_app;

-- Add comments for documentation
COMMENT ON TABLE journals IS 'Stores uploaded journal documents and their metadata';
COMMENT ON TABLE journal_interpretations IS 'Stores AI-generated interpretations of journal content';
COMMENT ON TABLE journal_processing_logs IS 'Audit trail for journal processing steps';
COMMENT ON VIEW journal_summary IS 'Summary view of journals with interpretation status';
COMMENT ON FUNCTION search_journals IS 'Full-text search function for journal content';

-- Insert sample data for testing (optional)
-- INSERT INTO journals (journal_id, title, content, journal_type, processing_status, metadata)
-- VALUES (
--     'sample_journal_001',
--     'Sample Medical Research Paper',
--     'This is a sample medical research paper about hypertension treatment...',
--     'RESEARCH_PAPER',
--     'COMPLETED',
--     '{"file_name": "sample.pdf", "file_size": 1024, "format": "PDF", "word_count": 500}'
-- );

COMMIT;
