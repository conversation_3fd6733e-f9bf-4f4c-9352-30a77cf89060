"""Integration tests for EHR journal integration workflow."""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from optimed.core.domain import (
    JournalEntry, JournalType, ProcessingStatus, DocumentMetadata, DocumentFormat
)
from optimed.adapters.fhir_hapi.repository import HAPIFHIRRepository
from optimed.services.ehr_journal_sync_service import EHRJournalSyncService


class TestEHRJournalIntegration:
    """Test EHR journal integration workflow."""

    @pytest.fixture
    def mock_fhir_repository(self):
        """Create mock FHIR repository."""
        repo = AsyncMock(spec=HAPIFHIRRepository)
        
        # Mock clinical notes data
        mock_journal_entry = JournalEntry(
            journal_id="fhir_doc_12345",
            patient_id="patient_123",
            title="Discharge Summary - Hypertension Management",
            content="Patient discharged with improved blood pressure control. Continue ACE inhibitor therapy.",
            journal_type=JournalType.CLINICAL_NOTE,
            metadata=DocumentMetadata(
                file_name="discharge_summary.txt",
                file_size=500,
                format=DocumentFormat.TEXT,
                word_count=50
            ),
            processing_status=ProcessingStatus.PENDING,
            uploaded_at=datetime.now(timezone.utc)
        )
        
        repo.get_patient_clinical_notes.return_value = [mock_journal_entry]
        return repo

    @pytest.fixture
    def mock_journal_repository(self):
        """Create mock journal repository."""
        repo = AsyncMock()
        repo.get_journal.return_value = None  # No existing journal
        repo.save_journal.return_value = AsyncMock()  # Return saved journal
        repo.get_patient_journals.return_value = []
        return repo

    @pytest.fixture
    def ehr_sync_service(self, mock_fhir_repository, mock_journal_repository):
        """Create EHR sync service with mocked dependencies."""
        return EHRJournalSyncService(
            fhir_repository=mock_fhir_repository,
            journal_repository=mock_journal_repository
        )

    @pytest.mark.asyncio
    async def test_sync_patient_clinical_notes_success(self, ehr_sync_service, mock_fhir_repository, mock_journal_repository):
        """Test successful sync of patient clinical notes."""
        patient_id = "patient_123"
        
        # Mock the saved journal entry
        saved_entry = JournalEntry(
            journal_id="fhir_doc_12345",
            patient_id=patient_id,
            title="Discharge Summary - Hypertension Management",
            content="Patient discharged with improved blood pressure control.",
            journal_type=JournalType.CLINICAL_NOTE,
            metadata=DocumentMetadata(
                file_name="discharge_summary.txt",
                file_size=500,
                format=DocumentFormat.TEXT,
                word_count=50
            ),
            processing_status=ProcessingStatus.PENDING,
            uploaded_at=datetime.now(timezone.utc)
        )
        mock_journal_repository.save_journal.return_value = saved_entry
        
        # Sync clinical notes
        result = await ehr_sync_service.sync_patient_clinical_notes(patient_id)
        
        # Verify results
        assert len(result) == 1
        assert result[0].journal_id == "fhir_doc_12345"
        assert result[0].patient_id == patient_id
        assert result[0].journal_type == JournalType.CLINICAL_NOTE
        
        # Verify repository calls
        mock_fhir_repository.get_patient_clinical_notes.assert_called_once_with(patient_id)
        mock_journal_repository.get_journal.assert_called_once_with("fhir_doc_12345")
        mock_journal_repository.save_journal.assert_called_once()

    @pytest.mark.asyncio
    async def test_sync_no_clinical_notes_available(self, ehr_sync_service, mock_fhir_repository):
        """Test sync when no clinical notes are available."""
        patient_id = "patient_456"
        
        # Mock no clinical notes
        mock_fhir_repository.get_patient_clinical_notes.return_value = []
        
        # Sync clinical notes
        result = await ehr_sync_service.sync_patient_clinical_notes(patient_id)
        
        # Verify no entries created
        assert len(result) == 0
        mock_fhir_repository.get_patient_clinical_notes.assert_called_once_with(patient_id)

    @pytest.mark.asyncio
    async def test_sync_skip_existing_journals(self, ehr_sync_service, mock_fhir_repository, mock_journal_repository):
        """Test that existing journals are skipped during sync."""
        patient_id = "patient_123"
        
        # Mock existing journal
        existing_journal = JournalEntry(
            journal_id="fhir_doc_12345",
            patient_id=patient_id,
            title="Existing Journal",
            content="Existing content",
            journal_type=JournalType.CLINICAL_NOTE,
            metadata=DocumentMetadata(
                file_name="existing.txt",
                file_size=100,
                format=DocumentFormat.TEXT
            ),
            processing_status=ProcessingStatus.COMPLETED
        )
        mock_journal_repository.get_journal.return_value = existing_journal
        
        # Sync clinical notes
        result = await ehr_sync_service.sync_patient_clinical_notes(patient_id)
        
        # Verify no new entries created (existing journal skipped)
        assert len(result) == 0
        mock_journal_repository.save_journal.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_sync_status(self, ehr_sync_service, mock_fhir_repository, mock_journal_repository):
        """Test getting sync status for a patient."""
        patient_id = "patient_123"
        
        # Mock existing EHR journals
        existing_journal = JournalEntry(
            journal_id="fhir_doc_12345",
            patient_id=patient_id,
            title="EHR Journal",
            content="Content",
            journal_type=JournalType.CLINICAL_NOTE,
            metadata=DocumentMetadata(
                file_name="ehr_note.txt",
                file_size=100,
                format=DocumentFormat.TEXT
            ),
            processing_status=ProcessingStatus.COMPLETED,
            uploaded_at=datetime.now(timezone.utc)
        )
        mock_journal_repository.get_patient_journals.return_value = [existing_journal]
        
        # Mock available clinical notes
        available_note = JournalEntry(
            journal_id="fhir_doc_67890",
            patient_id=patient_id,
            title="Available Note",
            content="Available content",
            journal_type=JournalType.CLINICAL_NOTE,
            metadata=DocumentMetadata(
                file_name="available.txt",
                file_size=200,
                format=DocumentFormat.TEXT
            ),
            processing_status=ProcessingStatus.PENDING
        )
        mock_fhir_repository.get_patient_clinical_notes.return_value = [available_note]
        
        # Get sync status
        status = await ehr_sync_service.get_sync_status(patient_id)
        
        # Verify status
        assert status['patient_id'] == patient_id
        assert status['total_ehr_journals'] == 1
        assert status['available_clinical_notes'] == 1
        assert status['pending_sync'] == 0  # 1 available - 1 existing = 0
        assert len(status['ehr_journals']) == 1
        assert status['ehr_journals'][0]['journal_id'] == "fhir_doc_12345"

    @pytest.mark.asyncio
    async def test_fhir_document_reference_conversion(self):
        """Test FHIR DocumentReference to JournalEntry conversion."""
        from fhir.resources.documentreference import DocumentReference
        from fhir.resources.attachment import Attachment
        from fhir.resources.codeableconcept import CodeableConcept
        from fhir.resources.coding import Coding
        
        # Create mock DocumentReference
        doc_ref = DocumentReference(
            id="doc_123",
            status="current",
            type=CodeableConcept(
                coding=[Coding(
                    system="http://loinc.org",
                    code="18842-5",
                    display="Discharge summary"
                )]
            ),
            subject={"reference": "Patient/patient_123"},
            date=datetime.now(timezone.utc),
            description="Hospital discharge summary",
            content=[{
                "attachment": Attachment(
                    contentType="text/plain",
                    data="UGF0aWVudCBkaXNjaGFyZ2VkIHdpdGggaW1wcm92ZWQgY29uZGl0aW9u"  # base64 encoded
                )
            }]
        )
        
        # Create FHIR repository and test conversion
        repo = HAPIFHIRRepository()
        journal_entry = await repo._document_reference_to_journal(doc_ref, "patient_123")
        
        # Verify conversion
        assert journal_entry is not None
        assert journal_entry.journal_id == "fhir_doc_doc_123"
        assert journal_entry.patient_id == "patient_123"
        assert journal_entry.title == "Hospital discharge summary"
        assert journal_entry.journal_type == JournalType.CLINICAL_NOTE
        assert "Patient discharged" in journal_entry.content

    @pytest.mark.asyncio
    async def test_multiple_patients_sync(self, ehr_sync_service):
        """Test syncing clinical notes for multiple patients."""
        patient_ids = ["patient_123", "patient_456", "patient_789"]
        
        # Sync multiple patients
        results = await ehr_sync_service.sync_all_patients_clinical_notes(patient_ids)
        
        # Verify results structure
        assert len(results) == 3
        for patient_id in patient_ids:
            assert patient_id in results
            assert isinstance(results[patient_id], list)

    def test_journal_type_determination(self):
        """Test journal type determination from DocumentReference."""
        from fhir.resources.documentreference import DocumentReference
        from fhir.resources.codeableconcept import CodeableConcept
        from fhir.resources.coding import Coding
        
        repo = HAPIFHIRRepository()
        
        # Test discharge summary
        doc_ref = DocumentReference(
            id="doc_1",
            status="current",
            type=CodeableConcept(
                coding=[Coding(display="Discharge summary")]
            )
        )
        journal_type = repo._determine_journal_type_from_doc_ref(doc_ref)
        assert journal_type == JournalType.CLINICAL_NOTE
        
        # Test progress note
        doc_ref.type.coding[0].display = "Progress note"
        journal_type = repo._determine_journal_type_from_doc_ref(doc_ref)
        assert journal_type == JournalType.CLINICAL_NOTE
        
        # Test consultation note
        doc_ref.type.coding[0].display = "Consultation note"
        journal_type = repo._determine_journal_type_from_doc_ref(doc_ref)
        assert journal_type == JournalType.CLINICAL_NOTE
