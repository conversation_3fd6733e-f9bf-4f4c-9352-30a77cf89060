"""Integration tests for journal processing workflow."""

import pytest
import tempfile
import os
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timezone

from optimed.core.domain import (
    DocumentFormat, JournalType, ProcessingStatus, 
    DocumentMetadata, JournalEntry, JournalInterpretation, MedicalInsight
)
from optimed.adapters.document_parsers import TextParser, FormatDetectorImpl
from optimed.adapters.journal_interpreter.claude_interpreter import ClaudeJournalInterpreter
from optimed.adapters.journal_processor.langgraph_processor import LangGraphJournalProcessor


class TestJournalProcessingWorkflow:
    """Test end-to-end journal processing workflow."""

    @pytest.fixture
    def mock_llm_client(self):
        """Mock LLM client for testing."""
        mock_client = AsyncMock()
        
        # Mock interpretation response
        mock_client.chat.return_value = AsyncMock()
        mock_client.chat.return_value.content = """
        {
            "diagnosis": [
                {
                    "content": "Patient shows signs of hypertension",
                    "confidence": 0.85,
                    "evidence": "Blood pressure readings consistently above 140/90",
                    "medical_terms": ["hypertension", "blood pressure"],
                    "icd_codes": ["I10"]
                }
            ],
            "treatment": [
                {
                    "content": "ACE inhibitor therapy recommended",
                    "confidence": 0.8,
                    "evidence": "Standard first-line treatment for hypertension",
                    "medical_terms": ["ACE inhibitor", "therapy"],
                    "icd_codes": []
                }
            ]
        }
        """
        
        return mock_client

    @pytest.fixture
    def mock_journal_repository(self):
        """Mock journal repository for testing."""
        mock_repo = AsyncMock()
        mock_repo.store_journal.return_value = "journal_123"
        mock_repo.store_interpretation.return_value = "interp_123"
        return mock_repo

    @pytest.fixture
    def mock_vector_store(self):
        """Mock vector store for testing."""
        mock_store = AsyncMock()
        mock_store.store_journal_embedding.return_value = "embedding_123"
        return mock_store

    @pytest.fixture
    def mock_embed_function(self, monkeypatch):
        """Mock the embed function."""
        async def mock_embed(text):
            # Return a simple mock embedding
            return [0.1] * 384  # MiniLM embedding dimension
        
        monkeypatch.setattr("optimed.adapters.journal_processor.langgraph_processor.embed", mock_embed)
        return mock_embed

    @pytest.fixture
    def journal_processor(self, mock_llm_client, mock_journal_repository, mock_vector_store):
        """Create journal processor with mocked dependencies."""
        document_parser = TextParser()
        format_detector = FormatDetectorImpl()
        journal_interpreter = ClaudeJournalInterpreter(mock_llm_client)
        
        return LangGraphJournalProcessor(
            document_parser=document_parser,
            format_detector=format_detector,
            journal_interpreter=journal_interpreter,
            journal_repository=mock_journal_repository,
            vector_store=mock_vector_store
        )

    @pytest.mark.asyncio
    async def test_process_text_document_success(self, journal_processor, mock_embed_function):
        """Test successful processing of a text document."""
        # Create test document
        test_content = """
        Medical Research Study: Hypertension Treatment

        Abstract: This study investigates the effectiveness of ACE inhibitors
        in treating hypertension in elderly patients.

        Introduction: Hypertension affects millions of people worldwide and
        is a major risk factor for cardiovascular disease.

        Methods: We conducted a randomized controlled trial with 200 participants
        aged 65 and older, comparing ACE inhibitor therapy to placebo.

        Results: The treatment group showed a significant reduction in blood
        pressure compared to the control group (p < 0.001).

        Conclusion: ACE inhibitors are effective for treating hypertension
        in elderly patients with minimal side effects.
        """
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_path = f.name
        
        try:
            # Process the document
            result = await journal_processor.process_document(
                file_path=temp_path,
                filename="hypertension_study.txt",
                patient_id="patient_123",
                user_id="user_456",
                session_id="session_789"
            )
            
            # Verify successful processing
            assert result.success is True
            assert result.error_message is None
            assert result.journal_entry is not None
            assert result.interpretation is not None
            
            # Verify journal entry details
            journal = result.journal_entry
            assert journal.title == "Medical Research Study: Hypertension Treatment"
            assert journal.patient_id == "patient_123"
            assert journal.user_id == "user_456"
            assert journal.session_id == "session_789"
            assert journal.journal_type == JournalType.RESEARCH_PAPER
            assert journal.processing_status == ProcessingStatus.COMPLETED
            assert "ACE inhibitors" in journal.content
            
            # Verify interpretation
            interpretation = result.interpretation
            assert interpretation.journal_id == journal.journal_id
            assert len(interpretation.medical_insights) > 0
            assert interpretation.overall_confidence > 0
            
            # Verify processing metadata
            assert result.total_processing_time_seconds is not None
            assert result.total_processing_time_seconds > 0
            
        finally:
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_process_document_from_bytes(self, journal_processor, mock_embed_function):
        """Test processing document from bytes."""
        test_content = """
        Case Study: Acute Myocardial Infarction

        Patient: 58-year-old male presented to emergency department
        with chest pain and shortness of breath.

        History: Patient has a history of smoking and hypertension.
        No previous cardiac events.

        Examination: ECG showed ST-elevation in leads II, III, and aVF.
        Troponin levels were elevated.

        Diagnosis: Acute inferior myocardial infarction.

        Treatment: Patient underwent primary percutaneous coronary
        intervention with stent placement.

        Outcome: Patient recovered well and was discharged on day 3
        with appropriate medications.
        """
        
        content_bytes = test_content.encode('utf-8')
        
        # Process the document
        result = await journal_processor.process_document(
            file_path=content_bytes,
            filename="mi_case_study.txt",
            patient_id="patient_456"
        )
        
        # Verify successful processing
        assert result.success is True
        assert result.journal_entry is not None
        
        # Verify journal type detection
        journal = result.journal_entry
        assert journal.journal_type == JournalType.CASE_STUDY
        assert "myocardial infarction" in journal.content.lower()

    @pytest.mark.asyncio
    async def test_process_document_parsing_failure(self, journal_processor):
        """Test handling of document parsing failure."""
        # Try to process a non-existent file
        result = await journal_processor.process_document(
            file_path="/non/existent/file.txt",
            filename="nonexistent.txt"
        )
        
        # Verify failure handling
        assert result.success is False
        assert result.error_message is not None
        assert "not found" in result.error_message.lower() or "failed" in result.error_message.lower()

    @pytest.mark.asyncio
    async def test_reprocess_journal(self, journal_processor, mock_journal_repository, mock_embed_function):
        """Test reprocessing an existing journal."""
        # Create mock existing journal
        metadata = DocumentMetadata(
            file_name="existing.txt",
            file_size=1000,
            format=DocumentFormat.TXT,
            word_count=200
        )
        
        existing_journal = JournalEntry(
            journal_id="existing_journal_123",
            title="Existing Medical Study",
            content="This is existing medical content about diabetes treatment.",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=metadata,
            processing_status=ProcessingStatus.COMPLETED
        )
        
        # Mock repository to return existing journal
        mock_journal_repository.get_journal.return_value = existing_journal
        
        # Reprocess the journal
        result = await journal_processor.reprocess_journal("existing_journal_123")
        
        # Verify successful reprocessing
        assert result.success is True
        assert result.journal_entry == existing_journal
        assert result.interpretation is not None
        
        # Verify repository calls
        mock_journal_repository.get_journal.assert_called_once_with("existing_journal_123")
        mock_journal_repository.store_interpretation.assert_called_once()

    @pytest.mark.asyncio
    async def test_reprocess_nonexistent_journal(self, journal_processor, mock_journal_repository):
        """Test reprocessing a non-existent journal."""
        # Mock repository to return None
        mock_journal_repository.get_journal.return_value = None
        
        # Attempt to reprocess
        with pytest.raises(ValueError, match="Journal not found"):
            await journal_processor.reprocess_journal("nonexistent_journal")

    @pytest.mark.asyncio
    async def test_process_with_interpretation_failure(self, journal_processor, mock_llm_client, mock_embed_function):
        """Test processing when interpretation fails but parsing succeeds."""
        # Make LLM client raise an exception
        mock_llm_client.chat.side_effect = Exception("LLM service unavailable")
        
        test_content = "Simple medical document content about patient care."
        content_bytes = test_content.encode('utf-8')
        
        # Process the document
        result = await journal_processor.process_document(
            file_path=content_bytes,
            filename="simple.txt"
        )
        
        # Should succeed overall but with warnings
        assert result.success is True
        assert result.journal_entry is not None
        assert len(result.warnings) > 0
        assert any("interpretation failed" in warning.lower() for warning in result.warnings)

    @pytest.mark.asyncio
    async def test_process_with_embedding_failure(self, journal_processor, mock_embed_function, monkeypatch):
        """Test processing when embedding generation fails."""
        # Make embed function raise an exception
        async def failing_embed(text):
            raise Exception("Embedding service unavailable")
        
        monkeypatch.setattr("optimed.adapters.journal_processor.langgraph_processor.embed", failing_embed)
        
        test_content = "Medical document for embedding test."
        content_bytes = test_content.encode('utf-8')
        
        # Process the document
        result = await journal_processor.process_document(
            file_path=content_bytes,
            filename="embedding_test.txt"
        )
        
        # Should succeed overall but with warnings
        assert result.success is True
        assert result.journal_entry is not None
        assert len(result.warnings) > 0
        assert any("embedding" in warning.lower() for warning in result.warnings)
        assert result.embedding_stored is False


class TestJournalInterpreterIntegration:
    """Test journal interpreter integration."""

    @pytest.fixture
    def mock_llm_client(self):
        """Mock LLM client with realistic responses."""
        mock_client = AsyncMock()
        return mock_client

    @pytest.fixture
    def journal_interpreter(self, mock_llm_client):
        """Create journal interpreter with mock LLM."""
        return ClaudeJournalInterpreter(mock_llm_client)

    @pytest.mark.asyncio
    async def test_interpret_research_paper(self, journal_interpreter, mock_llm_client):
        """Test interpreting a research paper."""
        # Mock LLM responses
        mock_llm_client.chat.side_effect = [
            # Summary response
            AsyncMock(content="This study demonstrates the effectiveness of ACE inhibitors in treating hypertension."),
            # Insights response
            AsyncMock(content='{"diagnosis": [{"content": "Hypertension diagnosis", "confidence": 0.9, "evidence": "Blood pressure data", "medical_terms": ["hypertension"], "icd_codes": ["I10"]}]}'),
            # Clinical analysis response
            AsyncMock(content='{"clinical_significance": "High clinical relevance", "key_findings": ["ACE inhibitors effective"], "treatment_implications": ["First-line therapy"], "diagnostic_implications": ["Blood pressure monitoring"], "quality_score": 0.8, "evidence_level": "Level I"}'),
            # Medical entities response
            AsyncMock(content='{"diseases": ["hypertension"], "medications": ["ACE inhibitors"], "procedures": ["blood pressure measurement"], "symptoms": ["elevated blood pressure"], "risk_factors": ["age", "smoking"]}')
        ]
        
        # Create test journal entry
        metadata = DocumentMetadata(
            file_name="research.pdf",
            file_size=2000,
            format=DocumentFormat.PDF,
            word_count=1000
        )
        
        journal_entry = JournalEntry(
            journal_id="journal_123",
            title="ACE Inhibitors for Hypertension",
            content="This research study evaluates the effectiveness of ACE inhibitors in treating hypertension in elderly patients...",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=metadata
        )
        
        # Interpret the journal
        interpretation = await journal_interpreter.interpret(journal_entry)
        
        # Verify interpretation
        assert interpretation.journal_id == "journal_123"
        assert interpretation.summary is not None
        assert len(interpretation.key_findings) > 0
        assert interpretation.clinical_significance is not None
        assert interpretation.overall_confidence > 0
        assert len(interpretation.diseases) > 0
        assert len(interpretation.medications) > 0
        
        # Verify LLM was called multiple times for different aspects
        assert mock_llm_client.chat.call_count == 4

    @pytest.mark.asyncio
    async def test_extract_insights_with_categories(self, journal_interpreter, mock_llm_client):
        """Test extracting insights with different categories."""
        # Mock insights response
        mock_llm_client.chat.return_value = AsyncMock(content="""
        {
            "diagnosis": [
                {
                    "content": "Patient diagnosed with Type 2 diabetes",
                    "confidence": 0.95,
                    "evidence": "HbA1c levels above 6.5%",
                    "medical_terms": ["diabetes", "HbA1c"],
                    "icd_codes": ["E11"]
                }
            ],
            "treatment": [
                {
                    "content": "Metformin therapy initiated",
                    "confidence": 0.85,
                    "evidence": "First-line treatment for T2DM",
                    "medical_terms": ["metformin"],
                    "icd_codes": []
                }
            ],
            "prognosis": [
                {
                    "content": "Good prognosis with proper management",
                    "confidence": 0.8,
                    "evidence": "Early diagnosis and treatment",
                    "medical_terms": ["prognosis"],
                    "icd_codes": []
                }
            ]
        }
        """)
        
        content = "Patient case study involving diabetes diagnosis and treatment with metformin."
        
        insights = await journal_interpreter.extract_insights(content, JournalType.CASE_STUDY)
        
        # Verify insights
        assert len(insights) == 3
        
        # Check categories
        categories = [insight.category for insight in insights]
        assert "diagnosis" in categories
        assert "treatment" in categories
        assert "prognosis" in categories
        
        # Check confidence levels
        for insight in insights:
            assert 0.0 <= insight.confidence <= 1.0
            assert insight.confidence_level is not None
