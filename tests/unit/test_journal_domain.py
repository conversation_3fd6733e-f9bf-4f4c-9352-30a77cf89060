"""Tests for journal domain models."""

import pytest
from datetime import datetime, timezone
from optimed.core.domain import (
    DocumentMetadata, JournalEntry, MedicalInsight, JournalInterpretation,
    ProcessingResult, DocumentFormat, JournalType, ProcessingStatus,
    InterpretationConfidence
)


class TestDocumentMetadata:
    """Test DocumentMetadata domain model."""

    def test_create_basic_metadata(self):
        """Test creating basic document metadata."""
        metadata = DocumentMetadata(
            file_name="test.pdf",
            file_size=1024,
            format=DocumentFormat.PDF
        )
        
        assert metadata.file_name == "test.pdf"
        assert metadata.file_size == 1024
        assert metadata.format == DocumentFormat.PDF
        assert metadata.mime_type is None

    def test_validate_file_size(self):
        """Test file size validation."""
        with pytest.raises(ValueError, match="File size must be non-negative"):
            DocumentMetadata(
                file_name="test.pdf",
                file_size=-1,
                format=DocumentFormat.PDF
            )

    def test_metadata_with_all_fields(self):
        """Test metadata with all optional fields."""
        now = datetime.now(timezone.utc)
        
        metadata = DocumentMetadata(
            file_name="research_paper.pdf",
            file_size=2048,
            format=DocumentFormat.PDF,
            mime_type="application/pdf",
            encoding="utf-8",
            page_count=10,
            word_count=5000,
            language="en",
            created_at=now,
            modified_at=now,
            author="Dr. Smith",
            title="Medical Research Study",
            subject="Cardiology",
            keywords=["heart", "research", "clinical"]
        )
        
        assert metadata.page_count == 10
        assert metadata.word_count == 5000
        assert metadata.language == "en"
        assert metadata.author == "Dr. Smith"
        assert metadata.title == "Medical Research Study"
        assert len(metadata.keywords) == 3


class TestJournalEntry:
    """Test JournalEntry domain model."""

    @pytest.fixture
    def sample_metadata(self):
        """Sample document metadata."""
        return DocumentMetadata(
            file_name="test.pdf",
            file_size=1024,
            format=DocumentFormat.PDF,
            word_count=500
        )

    def test_create_journal_entry(self, sample_metadata):
        """Test creating a journal entry."""
        entry = JournalEntry(
            journal_id="journal_123",
            title="Test Journal",
            content="This is test content",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=sample_metadata
        )
        
        assert entry.journal_id == "journal_123"
        assert entry.title == "Test Journal"
        assert entry.content == "This is test content"
        assert entry.journal_type == JournalType.RESEARCH_PAPER
        assert entry.processing_status == ProcessingStatus.PENDING
        assert entry.uploaded_at is not None

    def test_journal_entry_with_patient(self, sample_metadata):
        """Test journal entry linked to patient."""
        entry = JournalEntry(
            journal_id="journal_123",
            patient_id="patient_456",
            title="Patient Case Study",
            content="Patient case content",
            journal_type=JournalType.CASE_STUDY,
            metadata=sample_metadata
        )
        
        assert entry.has_patient_context() is True
        assert entry.patient_id == "patient_456"

    def test_journal_entry_without_patient(self, sample_metadata):
        """Test journal entry without patient link."""
        entry = JournalEntry(
            journal_id="journal_123",
            title="General Research",
            content="General research content",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=sample_metadata
        )
        
        assert entry.has_patient_context() is False
        assert entry.patient_id is None

    def test_get_word_count(self, sample_metadata):
        """Test word count calculation."""
        entry = JournalEntry(
            journal_id="journal_123",
            title="Test",
            content="This is a test with five words",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=sample_metadata
        )
        
        assert entry.get_word_count() == 8  # "This is a test with five words" = 7 words

    def test_get_section(self, sample_metadata):
        """Test section retrieval."""
        entry = JournalEntry(
            journal_id="journal_123",
            title="Test",
            content="Content",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=sample_metadata,
            sections={
                "abstract": "This is the abstract",
                "introduction": "This is the introduction"
            }
        )
        
        assert entry.get_section("abstract") == "This is the abstract"
        assert entry.get_section("Abstract") == "This is the abstract"  # Case insensitive
        assert entry.get_section("conclusion") is None


class TestMedicalInsight:
    """Test MedicalInsight domain model."""

    def test_create_medical_insight(self):
        """Test creating a medical insight."""
        insight = MedicalInsight(
            insight_id="insight_123",
            category="diagnosis",
            content="Patient shows signs of hypertension",
            confidence=0.85
        )
        
        assert insight.insight_id == "insight_123"
        assert insight.category == "diagnosis"
        assert insight.content == "Patient shows signs of hypertension"
        assert insight.confidence == 0.85
        assert insight.confidence_level == InterpretationConfidence.HIGH

    def test_confidence_validation(self):
        """Test confidence score validation."""
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            MedicalInsight(
                insight_id="insight_123",
                category="diagnosis",
                content="Test content",
                confidence=1.5
            )

    def test_confidence_level_mapping(self):
        """Test confidence level automatic mapping."""
        # Very high confidence
        insight_very_high = MedicalInsight(
            insight_id="1", category="test", content="test", confidence=0.95
        )
        assert insight_very_high.confidence_level == InterpretationConfidence.VERY_HIGH
        
        # High confidence
        insight_high = MedicalInsight(
            insight_id="2", category="test", content="test", confidence=0.75
        )
        assert insight_high.confidence_level == InterpretationConfidence.HIGH
        
        # Medium confidence
        insight_medium = MedicalInsight(
            insight_id="3", category="test", content="test", confidence=0.55
        )
        assert insight_medium.confidence_level == InterpretationConfidence.MEDIUM
        
        # Low confidence
        insight_low = MedicalInsight(
            insight_id="4", category="test", content="test", confidence=0.35
        )
        assert insight_low.confidence_level == InterpretationConfidence.LOW
        
        # Very low confidence
        insight_very_low = MedicalInsight(
            insight_id="5", category="test", content="test", confidence=0.15
        )
        assert insight_very_low.confidence_level == InterpretationConfidence.VERY_LOW


class TestJournalInterpretation:
    """Test JournalInterpretation domain model."""

    @pytest.fixture
    def sample_insights(self):
        """Sample medical insights."""
        return [
            MedicalInsight(
                insight_id="1",
                category="diagnosis",
                content="Hypertension diagnosis",
                confidence=0.9
            ),
            MedicalInsight(
                insight_id="2",
                category="treatment",
                content="ACE inhibitor treatment",
                confidence=0.8
            ),
            MedicalInsight(
                insight_id="3",
                category="diagnosis",
                content="Secondary diagnosis",
                confidence=0.6
            )
        ]

    def test_create_interpretation(self, sample_insights):
        """Test creating journal interpretation."""
        interpretation = JournalInterpretation(
            interpretation_id="interp_123",
            journal_id="journal_456",
            summary="This journal discusses hypertension treatment",
            medical_insights=sample_insights,
            clinical_significance="High clinical relevance",
            overall_confidence=0.8
        )
        
        assert interpretation.interpretation_id == "interp_123"
        assert interpretation.journal_id == "journal_456"
        assert len(interpretation.medical_insights) == 3
        assert interpretation.overall_confidence == 0.8
        assert interpretation.confidence_level == InterpretationConfidence.HIGH

    def test_get_high_confidence_insights(self, sample_insights):
        """Test filtering high confidence insights."""
        interpretation = JournalInterpretation(
            interpretation_id="interp_123",
            journal_id="journal_456",
            summary="Test summary",
            medical_insights=sample_insights,
            clinical_significance="Test significance",
            overall_confidence=0.8
        )
        
        high_confidence = interpretation.get_high_confidence_insights(min_confidence=0.75)
        assert len(high_confidence) == 2  # Only insights with confidence >= 0.75
        
        very_high_confidence = interpretation.get_high_confidence_insights(min_confidence=0.85)
        assert len(very_high_confidence) == 1  # Only insight with confidence >= 0.85

    def test_get_insights_by_category(self, sample_insights):
        """Test filtering insights by category."""
        interpretation = JournalInterpretation(
            interpretation_id="interp_123",
            journal_id="journal_456",
            summary="Test summary",
            medical_insights=sample_insights,
            clinical_significance="Test significance",
            overall_confidence=0.8
        )
        
        diagnosis_insights = interpretation.get_insights_by_category("diagnosis")
        assert len(diagnosis_insights) == 2
        
        treatment_insights = interpretation.get_insights_by_category("treatment")
        assert len(treatment_insights) == 1
        
        nonexistent_insights = interpretation.get_insights_by_category("nonexistent")
        assert len(nonexistent_insights) == 0


class TestProcessingResult:
    """Test ProcessingResult domain model."""

    @pytest.fixture
    def sample_journal_entry(self):
        """Sample journal entry."""
        metadata = DocumentMetadata(
            file_name="test.pdf",
            file_size=1024,
            format=DocumentFormat.PDF
        )
        
        return JournalEntry(
            journal_id="journal_123",
            title="Test Journal",
            content="Test content",
            journal_type=JournalType.RESEARCH_PAPER,
            metadata=metadata
        )

    def test_successful_processing_result(self, sample_journal_entry):
        """Test successful processing result."""
        start_time = datetime.now(timezone.utc)
        end_time = datetime(2024, 1, 1, 12, 0, 5, tzinfo=timezone.utc)
        
        result = ProcessingResult(
            result_id="result_123",
            journal_entry=sample_journal_entry,
            processing_started_at=start_time,
            processing_completed_at=end_time,
            success=True
        )
        
        assert result.result_id == "result_123"
        assert result.success is True
        assert result.journal_entry == sample_journal_entry
        assert result.is_complete() is False  # No interpretation provided

    def test_failed_processing_result(self, sample_journal_entry):
        """Test failed processing result."""
        start_time = datetime.now(timezone.utc)
        
        result = ProcessingResult(
            result_id="result_123",
            journal_entry=sample_journal_entry,
            processing_started_at=start_time,
            success=False,
            error_message="Processing failed due to invalid format"
        )
        
        assert result.success is False
        assert result.error_message == "Processing failed due to invalid format"
        assert result.is_complete() is False

    def test_processing_duration_calculation(self, sample_journal_entry):
        """Test processing duration calculation."""
        start_time = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        end_time = datetime(2024, 1, 1, 12, 0, 5, tzinfo=timezone.utc)
        
        result = ProcessingResult(
            result_id="result_123",
            journal_entry=sample_journal_entry,
            processing_started_at=start_time,
            processing_completed_at=end_time,
            success=True
        )
        
        duration = result.get_processing_duration()
        assert duration == 5.0  # 5 seconds
