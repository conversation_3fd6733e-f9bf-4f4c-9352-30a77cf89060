"""Tests for document parser adapters."""

import pytest
import tempfile
import os
from pathlib import Path

from optimed.core.domain import DocumentFormat, JournalType
from optimed.adapters.document_parsers import (
    TextParser, FormatDetectorImpl
)


class TestTextParser:
    """Test TextParser adapter."""

    @pytest.fixture
    def text_parser(self):
        """Create TextParser instance."""
        return TextParser()

    def test_supports_format(self, text_parser):
        """Test format support checking."""
        assert text_parser.supports_format(DocumentFormat.TXT) is True
        assert text_parser.supports_format(DocumentFormat.MARKDOWN) is True
        assert text_parser.supports_format(DocumentFormat.PDF) is False

    @pytest.mark.asyncio
    async def test_parse_text_file(self, text_parser):
        """Test parsing a text file."""
        # Create temporary text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test document.\nIt has multiple lines.\nAnd some medical keywords like diagnosis and treatment.")
            temp_path = f.name
        
        try:
            content, metadata = await text_parser.parse(temp_path, DocumentFormat.TXT)
            
            assert "This is a test document" in content
            assert "medical keywords" in content
            assert metadata.file_name == Path(temp_path).name
            assert metadata.format == DocumentFormat.TXT
            assert metadata.word_count > 0
            assert "diagnosis" in metadata.keywords
            assert "treatment" in metadata.keywords
            
        finally:
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_parse_bytes(self, text_parser):
        """Test parsing text from bytes."""
        text_content = "This is a medical document about patient diagnosis and treatment protocols."
        text_bytes = text_content.encode('utf-8')
        
        content, metadata = await text_parser.parse(text_bytes, DocumentFormat.TXT)
        
        assert content == text_content
        assert metadata.file_size == len(text_bytes)
        assert metadata.word_count > 0
        assert "diagnosis" in metadata.keywords
        assert "treatment" in metadata.keywords

    @pytest.mark.asyncio
    async def test_parse_markdown_with_title(self, text_parser):
        """Test parsing markdown with title extraction."""
        markdown_content = """# Medical Research Study

This is a research study about cardiovascular disease.

## Methods

We conducted a clinical trial with 100 patients.

## Results

The treatment showed significant improvement.
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(markdown_content)
            temp_path = f.name
        
        try:
            content, metadata = await text_parser.parse(temp_path, DocumentFormat.MARKDOWN)
            
            assert content == markdown_content
            assert metadata.title == "Medical Research Study"
            assert "clinical" in metadata.keywords
            assert "treatment" in metadata.keywords
            
        finally:
            os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_encoding_detection(self, text_parser):
        """Test encoding detection for different text encodings."""
        # Test UTF-8 content
        utf8_content = "This is a test with special characters: café, naïve"
        utf8_bytes = utf8_content.encode('utf-8')
        
        content, metadata = await text_parser.parse(utf8_bytes, DocumentFormat.TXT)
        assert content == utf8_content
        
        # Test Latin-1 content
        latin1_content = "This is a test with Latin-1 characters"
        latin1_bytes = latin1_content.encode('latin-1')
        
        content, metadata = await text_parser.parse(latin1_bytes, DocumentFormat.TXT)
        assert content == latin1_content


class TestFormatDetector:
    """Test FormatDetectorImpl adapter."""

    @pytest.fixture
    def format_detector(self):
        """Create FormatDetectorImpl instance."""
        return FormatDetectorImpl()

    @pytest.mark.asyncio
    async def test_detect_format_by_extension(self, format_detector):
        """Test format detection by file extension."""
        # Test various extensions
        assert await format_detector.detect_format("test.pdf") == DocumentFormat.PDF
        assert await format_detector.detect_format("test.docx") == DocumentFormat.DOCX
        assert await format_detector.detect_format("test.txt") == DocumentFormat.TXT
        assert await format_detector.detect_format("test.json") == DocumentFormat.JSON
        assert await format_detector.detect_format("test.md") == DocumentFormat.MARKDOWN

    @pytest.mark.asyncio
    async def test_detect_format_by_filename_parameter(self, format_detector):
        """Test format detection using filename parameter."""
        # When file_path is bytes, use filename parameter
        result = await format_detector.detect_format(b"some bytes", filename="document.pdf")
        assert result == DocumentFormat.PDF

    @pytest.mark.asyncio
    async def test_detect_format_by_content(self, format_detector):
        """Test format detection by examining content."""
        # PDF signature
        pdf_bytes = b'%PDF-1.4\n%some pdf content'
        result = await format_detector.detect_format(pdf_bytes)
        assert result == DocumentFormat.PDF
        
        # JSON content
        json_bytes = b'{"title": "Test Document", "content": "Some content"}'
        result = await format_detector.detect_format(json_bytes)
        assert result == DocumentFormat.JSON
        
        # XML content
        xml_bytes = b'<document><title>Test</title><content>Some content</content></document>'
        result = await format_detector.detect_format(xml_bytes)
        assert result == DocumentFormat.XML
        
        # Plain text
        text_bytes = b'This is just plain text content'
        result = await format_detector.detect_format(text_bytes)
        assert result == DocumentFormat.TXT

    @pytest.mark.asyncio
    async def test_detect_unknown_format(self, format_detector):
        """Test detection of unknown format."""
        result = await format_detector.detect_format("test.unknown")
        assert result == DocumentFormat.UNKNOWN
        
        # Very short content
        result = await format_detector.detect_format(b"ab")
        assert result == DocumentFormat.UNKNOWN

    @pytest.mark.asyncio
    async def test_detect_journal_type(self, format_detector):
        """Test journal type detection from content."""
        from optimed.core.domain import DocumentMetadata
        
        # Research paper content
        research_content = """
        Abstract: This study investigates the effects of medication X.
        Introduction: Previous research has shown...
        Methods: We conducted a randomized controlled trial.
        Results: The treatment group showed significant improvement.
        Discussion: These findings suggest...
        Conclusion: Medication X is effective for treating condition Y.
        References: [1] Smith et al. (2020)...
        """
        
        metadata = DocumentMetadata(
            file_name="research.pdf",
            file_size=1000,
            format=DocumentFormat.PDF
        )
        
        journal_type = await format_detector.detect_journal_type(research_content, metadata)
        assert journal_type == JournalType.RESEARCH_PAPER
        
        # Case study content
        case_content = """
        Case Report: A 45-year-old patient presented with chest pain.
        Patient History: The patient had a history of hypertension.
        Clinical Presentation: Physical examination revealed...
        """
        
        journal_type = await format_detector.detect_journal_type(case_content, metadata)
        assert journal_type == JournalType.CASE_STUDY
        
        # Clinical trial content
        trial_content = """
        This randomized controlled clinical trial evaluated the efficacy
        of intervention X in 200 participants. The primary endpoint was...
        """
        
        journal_type = await format_detector.detect_journal_type(trial_content, metadata)
        assert journal_type == JournalType.CLINICAL_TRIAL
        
        # Lab report content
        lab_content = """
        Laboratory Results:
        Specimen: Blood
        Test Results:
        - Hemoglobin: 12.5 g/dL
        - White Blood Cell Count: 7,200/μL
        Analysis: Results are within normal limits.
        """
        
        journal_type = await format_detector.detect_journal_type(lab_content, metadata)
        assert journal_type == JournalType.LAB_REPORT
        
        # Unknown content
        unknown_content = "This is just some random text without medical context."
        journal_type = await format_detector.detect_journal_type(unknown_content, metadata)
        assert journal_type == JournalType.UNKNOWN


class TestBaseDocumentParser:
    """Test BaseDocumentParser functionality."""

    def test_language_detection(self):
        """Test simple language detection."""
        from optimed.adapters.document_parsers.base import BaseDocumentParser
        
        parser = BaseDocumentParser()
        
        # English text
        english_text = "The patient was admitted to the hospital for treatment and care."
        language = parser._detect_language(english_text)
        assert language == "en"
        
        # Non-English text (should return unknown)
        foreign_text = "Este es un texto en español sin palabras en inglés comunes."
        language = parser._detect_language(foreign_text)
        assert language == "unknown"

    def test_title_extraction(self):
        """Test title extraction from content."""
        from optimed.adapters.document_parsers.base import BaseDocumentParser
        
        parser = BaseDocumentParser()
        
        # Content with clear title
        content_with_title = """Medical Research Study on Hypertension

This study investigates the effects of ACE inhibitors on blood pressure.
The research was conducted over 12 months with 500 participants.
"""
        
        title = parser._extract_title(content_with_title)
        assert title == "Medical Research Study on Hypertension"
        
        # Content without clear title
        content_without_title = """
        
        This is just some content without a clear title at the beginning.
        It has multiple paragraphs but no obvious title structure.
        """
        
        title = parser._extract_title(content_without_title)
        assert title == "This is just some content without a clear title at the beginning."

    def test_keyword_extraction(self):
        """Test medical keyword extraction."""
        from optimed.adapters.document_parsers.base import BaseDocumentParser
        
        parser = BaseDocumentParser()
        
        content = """
        This medical study focuses on patient diagnosis and treatment protocols.
        The research involved clinical trials at the hospital with healthcare
        professionals including doctors and nurses. The therapy showed positive
        results in disease management and medication effectiveness.
        """
        
        keywords = parser._extract_keywords(content)
        
        # Should extract medical terms
        expected_keywords = ["medical", "patient", "diagnosis", "treatment", "clinical", 
                           "hospital", "healthcare", "doctor", "therapy", "disease"]
        
        for keyword in expected_keywords:
            if keyword in content.lower():
                assert keyword in keywords
        
        # Should limit to 10 keywords
        assert len(keywords) <= 10
